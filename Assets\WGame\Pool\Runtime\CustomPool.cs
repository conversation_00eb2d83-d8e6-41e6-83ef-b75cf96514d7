﻿
using UnityEngine;

public class CustomPool
{
#if UNITY_EDITOR
    public static bool collectionCheck = true;
#else
    public static bool collectionCheck = false;
#endif

    SimplePool<PoolItem> pool;
    GameObject prefab;
    Transform parent;
    public SimplePool<PoolItem> Create(GameObject prefab, Transform parent, int defaultCapacity = 10, int maxSize = 10000)
    {
        this.prefab = prefab;
        this.parent = parent;
        this.pool = new SimplePool<PoolItem>(CreatePoolItem, GetPoolItem, ReleasePoolItem, DestoryPoolItem, collectionCheck, defaultCapacity, maxSize);
        return this.pool;
    }

    public PoolItem CreatePoolItem()
    {
        var go = GameObject.Instantiate(prefab, parent);
        var poolItem = go.GetComponent<PoolItem>();
        if (poolItem == null)
        {
            Debug.LogError(prefab.name + " has no PoolItem component");
        }
        poolItem.owner = this;
        return poolItem;
    }

    public void Release(PoolItem item)
    {
        pool?.Release(item);
    }
    private void ReleasePoolItem(PoolItem obj)
    {
        obj.OnReleaseToPool();
    }
    private void GetPoolItem(PoolItem obj)
    {
        obj.OnSpawnFromPool();
    }
    private void DestoryPoolItem(PoolItem obj)
    {
        obj.DestroyFromPool();
    }
}