# This .gitignore file should be placed at the root of your Unity project directory

# Unity generated files
[Ll]ibrary/
[Tt]emp/
[Oo]bj/
[Bb]uild/
[Bb]uilds/
[Ll]ogs/
[Uu]ser[Ss]ettings/

# MemoryCaptures can get excessive in size.
# They also could contain extremely sensitive data
[Mm]emoryCaptures/

# Asset meta data should only be ignored when the corresponding asset is also ignored
!/[Aa]ssets/**/*.meta

# Uncomment this line if you wish to ignore the asset store tools plugin
# /[Aa]ssets/AssetStoreTools*

# Autogenerated Jetbrains Rider plugin
[Aa]ssets/Plugins/Editor/JetBrains*

# Visual Studio cache directory
.vs/

# Gradle cache directory
.gradle/

# Autogenerated VS/MD/Consulo solution and project files
ExportedObj/
.consulo/
*.csproj
*.unityproj
*.sln
*.suo
*.tmp
*.user
*.userprefs
*.pidb
*.booproj
*.svd
*.pdb
*.mdb
*.opendb
*.VC.db

# Unity3D generated meta files
*.pidb.meta
*.pdb.meta
*.mdb.meta

# Unity3D generated file on crash reports
sysinfo.txt

# Builds
*.apk
*.aab
*.unitypackage
*.app

# Crashlytics generated file
crashlytics-build.properties

# Packed Addressables
/[Aa]ssets/[Aa]ddressable[Aa]ssets[Dd]ata/*/*.bin*

# Temporary auto-generated Android Assets
/[Aa]ssets/[Ss]treamingAssets/aa.meta
/[Aa]ssets/[Ss]treamingAssets/aa/*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
$RECYCLE.BIN/
Desktop.ini

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Visual Studio Code
.vscode/

# Rider
.idea/

# Plastic SCM
.plastic/

# Unity Package Manager
[Pp]ackages/
manifest.json
packages-lock.json

# Unity Timeline
*.playable

# Fmod
[Ff]mod[Bb]ank/

# TextMeshPro
[Aa]ssets/TextMesh*Pro/

# Unity Collaborate
.collab/

# Unity Cloud Build
/cloudbuild/

# Unity Analytics
[Uu]nity[Aa]nalytics/

# Unity Purchasing
[Uu]nity[Pp]urchasing/

# Unity Ads
[Uu]nity[Aa]ds/

# Unity Remote Config
[Rr]emote[Cc]onfig/

# Unity Services
[Uu]nity[Ss]ervices/

# Unity WebGL
[Ww]eb[Gg][Ll]/

# Unity Addressables
[Aa]ddressable[Aa]ssets[Dd]ata/

# Unity Cloud Diagnostics
[Cc]loud[Dd]iagnostics/

# Unity Performance Reporting
[Pp]erformance[Rr]eporting/

# Unity Remote
[Rr]emote/

# Unity Test Framework
[Tt]est[Rr]unner/

# Unity Package Manager logs
upm.log

# Unity Editor logs
Editor.log

# Unity Profiler logs
profiler.data

# Unity Recorder
[Rr]ecordings/

# Unity Cinemachine
[Cc]inemachine/

# Unity ProBuilder
[Pp]ro[Bb]uilder[Dd]ata/

# Unity Terrain Tools
[Tt]errain[Tt]ools/

# Unity 2D Animation
[Aa]nimation[Cc]lips/

# Unity Input System
[Ii]nput[Ss]ystem/

# Unity Localization
[Ll]ocalization/

# Unity XR
[Xx][Rr]/

# Unity Netcode
[Nn]etcode/

# Unity Machine Learning
[Mm]l-[Aa]gents/

# Unity Burst
[Bb]urst[Dd]ebug[Ii]nformation_DoNotShip/

# Unity Jobs
[Jj]obs/

# Unity Mathematics
[Mm]athematics/

# Unity Collections
[Cc]ollections/

# Unity Entities
[Ee]ntities/

# Unity Physics
[Pp]hysics/

# Unity Rendering
[Rr]endering/

# Unity Scriptable Render Pipeline
[Ss]criptable[Rr]ender[Pp]ipeline/

# Unity High Definition Render Pipeline
[Hh]igh[Dd]efinition[Rr]ender[Pp]ipeline/

# Unity Universal Render Pipeline
[Uu]niversal[Rr]ender[Pp]ipeline/

# Unity Shader Graph
[Ss]hader[Gg]raph/

# Unity Visual Effect Graph
[Vv]isual[Ee]ffect[Gg]raph/

# Unity Post Processing
[Pp]ost[Pp]rocessing/

# Unity ProGrid
[Pp]ro[Gg]rid/

# Unity Polybrush
[Pp]olybrush/

# Unity 2D Tilemap Extras
[Tt]ilemap[Ee]xtras/

# Unity 2D Sprite Shape
[Ss]prite[Ss]hape/

# Unity 2D Pixel Perfect
[Pp]ixel[Pp]erfect/

# Unity 2D Lights
[Ll]ights/

# Unity 2D Shadow Casters
[Ss]hadow[Cc]asters/

# Unity 2D IK
[Ii][Kk]/

# Unity 2D Animation
[Aa]nimation/

# Unity 2D PSD Importer
[Pp]sd[Ii]mporter/

# Unity 2D Common
[Cc]ommon/

# Unity 2D Spline
[Ss]pline/

# Unity 2D Path
[Pp]ath/

# Unity 2D Bone
[Bb]one/

# Unity 2D Triangle
[Tt]riangle/

# Unity 2D Tessellation
[Tt]essellation/

# Unity 2D Deformation
[Dd]eformation/

# Unity 2D SpriteLibrary
[Ss]prite[Ll]ibrary/

# Unity 2D SpriteResolver
[Ss]prite[Rr]esolver/

# Unity 2D SpriteSwap
[Ss]prite[Ss]wap/

# Unity 2D SpriteSkin
[Ss]prite[Ss]kin/

# Unity 2D IKManager2D
[Ii][Kk][Mm]anager2[Dd]/

# Unity 2D CCDSolver2D
[Cc][Cc][Dd][Ss]olver2[Dd]/

# Unity 2D FabrikSolver2D
[Ff]abrik[Ss]olver2[Dd]/

# Unity 2D LimbSolver2D
[Ll]imb[Ss]olver2[Dd]/
