using System.Collections.Generic;
using UnityEngine;
using FairyGUI;

/// <summary>
/// 简化的操作层接口，封装复杂的层级管理逻辑和网格显示功能
/// </summary>
public class OperationLayer
{
    private SmartLayerManager layerManager;
    private JigsawPanel parentPanel;
    private GComponent rootContainer;
    private GComponent operationComponent;
    
    // 网格配置
    public int gridColumns = 6;
    public int gridRows = 8;
    public Color gridLineColor = Color.white;
    public float gridLineWidth = 1f;
    public bool showGrid = true;
    private GComponent layerGrid;
    
    public OperationLayer(GComponent opComponent, JigsawPanel panel, GComponent container)
    {
        operationComponent = opComponent;
        parentPanel = panel;
        rootContainer = container;
        
        // 初始化网格层
        layerGrid = new GComponent();
        layerGrid.touchable = false;
        operationComponent.AddChildAt(layerGrid, 1);
        
        // 初始化管理器
        layerManager = new SmartLayerManager(container, panel);
        
        DoInitialize();
    }
    
    private void DoInitialize()
    {
        // 设置网格线颜色为半透明白色
        gridLineColor = new Color(1f, 1f, 1f, 0.3f);

        // 绘制网格
        if (showGrid)
        {
            DrawGrid();
        }
    }
    
    /// <summary>
    /// 拼块拖拽开始事件
    /// </summary>
    /// <param name="piece">拼块</param>
    public void OnPieceDragStart(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed || layerManager == null) return;
        
        layerManager.StartDragging(piece);
    }
    
    /// <summary>
    /// 拼块拖拽结束事件
    /// </summary>
    /// <param name="piece">拼块</param>
    public void OnPieceDragEnd(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed || layerManager == null) return;
        
        layerManager.StopDragging(piece);
    }
    
    /// <summary>
    /// 拼块位置变化事件
    /// </summary>
    /// <param name="piece">拼块</param>
    public void OnPiecePositionChanged(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed || layerManager == null) return;
        
        layerManager.UpdatePiecePosition(piece);
    }
    
    /// <summary>
    /// 获取层级管理器（用于高级操作）
    /// </summary>
    /// <returns>智能层级管理器</returns>
    public SmartLayerManager GetLayerManager()
    {
        return layerManager;
    }
    
    
    /// <summary>
    /// 清空所有层级管理数据
    /// </summary>
    public void Clear()
    {
        if (layerManager != null)
        {
            layerManager.Clear();
        }
    }
    
    
    /// <summary>
    /// 绘制网格线
    /// </summary>
    private void DrawGrid()
    {
        if (operationComponent == null) return;

        float cellWidth = operationComponent.width / gridColumns;
        float cellHeight = operationComponent.height / gridRows;

        // 绘制网格线
        DrawGridLines(cellWidth, cellHeight);
    }
    
    /// <summary>
    /// 绘制网格线（使用矩形边框方式）
    /// </summary>
    private void DrawGridLines(float cellWidth, float cellHeight)
    {
        // 绘制垂直线
        for (int i = 1; i < gridColumns; i++)
        {
            float x = i * cellWidth;
            var lineGraph = new GGraph();
            lineGraph.name = $"gridLine_v_{i}";
            lineGraph.SetSize(gridLineWidth, operationComponent.height);
            lineGraph.SetXY(x - gridLineWidth / 2, 0);
            lineGraph.DrawRect(gridLineWidth, operationComponent.height, 0, Color.clear, gridLineColor);
            
            layerGrid.AddChild(lineGraph);
        }

        // 绘制水平线
        for (int i = 1; i < gridRows; i++)
        {
            float y = i * cellHeight;
            var lineGraph = new GGraph();
            lineGraph.name = $"gridLine_h_{i}";
            lineGraph.SetSize(operationComponent.width, gridLineWidth);
            lineGraph.SetXY(0, y - gridLineWidth / 2);
            lineGraph.DrawRect(operationComponent.width, gridLineWidth, 0, Color.clear, gridLineColor);
            
            layerGrid.AddChild(lineGraph);
        }
    }
    
    /// <summary>
    /// 设置网格显示状态
    /// </summary>
    /// <param name="visible">是否显示网格</param>
    public void SetGridVisible(bool visible)
    {
        showGrid = visible;
        
        if (showGrid)
        {
            DrawGrid();
        }
        else
        {
            ClearGrid();
        }
    }
    
    /// <summary>
    /// 清除网格显示
    /// </summary>
    private void ClearGrid()
    {
        if (layerGrid == null) return;

        // 移除所有网格线
        for (int i = layerGrid.numChildren - 1; i >= 0; i--)
        {
            var child = layerGrid.GetChildAt(i);
            if (child.name != null && child.name.StartsWith("gridLine_"))
            {
                layerGrid.RemoveChildAt(i, true);
            }
        }
    }

    
    /// <summary>
    /// 获取指定位置对应的网格坐标
    /// </summary>
    /// <param name="localPosition">本地坐标</param>
    /// <returns>网格坐标</returns>
    public Vector2Int GetGridPosition(Vector2 localPosition)
    {
        if (operationComponent == null) return Vector2Int.zero;

        float cellWidth = operationComponent.width / gridColumns;
        float cellHeight = operationComponent.height / gridRows;

        int gridX = Mathf.FloorToInt(localPosition.x / cellWidth);
        int gridY = Mathf.FloorToInt(localPosition.y / cellHeight);

        // 限制在网格范围内
        gridX = Mathf.Clamp(gridX, 0, gridColumns - 1);
        gridY = Mathf.Clamp(gridY, 0, gridRows - 1);

        return new Vector2Int(gridX, gridY);
    }

    /// <summary>
    /// 获取网格坐标对应的本地位置（网格中心点）
    /// </summary>
    /// <param name="gridPosition">网格坐标</param>
    /// <returns>本地坐标</returns>
    public Vector2 GetLocalPosition(Vector2Int gridPosition)
    {
        if (operationComponent == null) return Vector2.zero;

        float cellWidth = operationComponent.width / gridColumns;
        float cellHeight = operationComponent.height / gridRows;

        float x = (gridPosition.x + 0.5f) * cellWidth;
        float y = (gridPosition.y + 0.5f) * cellHeight;

        return new Vector2(x, y);
    }
}