//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.1
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.Text;
using System.Security.Cryptography;
using System.IO;

public class MD5Util
{
	public static string Encrypt(string input)
	{
		MD5 md5 = MD5.Create(); 
		byte[] inputBytes = System.Text.Encoding.ASCII.GetBytes(input); 
		byte[] hash = md5.ComputeHash(inputBytes); 
		StringBuilder sb = new StringBuilder(); 
		for (int i = 0; i < hash.Length; i++) 
		{
			sb.Append(hash[i].ToString("x2"));//x2 lowercase, X2 uppercase
		}
		return sb.ToString();
	}


	public static string GetFileHash(string filePath)
	{           
		try
		{
			FileStream fs = File.Open(filePath, FileMode.Open);
			int len = (int)fs.Length;
			byte[] data = new byte[len];
			fs.Read(data, 0, len);
			fs.Close();
			fs.Dispose();
			MD5 md5 = new MD5CryptoServiceProvider();
			byte[] result = md5.ComputeHash(data);
			string fileMD5 = "";
			foreach (byte b in result)
			{
				fileMD5 += Convert.ToString(b, 16);
			}
			return fileMD5;
		}
		catch (Exception e)
		{
			UnityEngine.Debug.LogException(e);
			return "";
		}
	}

	public static string GetFileHash(byte[] data)
	{           
		try
		{
			MD5 md5 = new MD5CryptoServiceProvider();
			byte[] result = md5.ComputeHash(data);
			string fileMD5 = "";
			foreach (byte b in result)
			{
				fileMD5 += Convert.ToString(b, 16);
			}
			return fileMD5;
		}
		catch (Exception e)
		{
            UnityEngine.Debug.LogException(e);
			return "";
		}
	}

}

