using UnityEngine;
using System.Collections.Generic;

namespace JigsawGenerator
{
    [System.Serializable]
    public class JigsawPiece
    {
        public int x;
        public int y;
        public List<Vector2> outline = new List<Vector2>();
        public Mesh mesh;

        // 拼图片的边界
        public float minX, maxX, minY, maxY;

        // 整个拼图的尺寸信息（用于正确计算UV坐标）
        public float puzzleWidth, puzzleHeight;

        public JigsawPiece(int x, int y)
        {
            this.x = x;
            this.y = y;
        }

        public void CalculateBounds()
        {
            if (outline.Count == 0)
                return;

            minX = maxX = outline[0].x;
            minY = maxY = outline[0].y;

            for (int i = 1; i < outline.Count; i++)
            {
                Vector2 point = outline[i];

                if (point.x < minX) minX = point.x;
                if (point.x > maxX) maxX = point.x;
                if (point.y < minY) minY = point.y;
                if (point.y > maxY) maxY = point.y;
            }
        }

        public void GenerateMesh()
        {
            if (outline.Count < 3)
            {
                Debug.LogError($"拼块 ({x}, {y}) 轮廓点数不足: {outline.Count}");
                return;
            }

            mesh = new Mesh();

            // 使用Triangulator将轮廓三角化
            Triangulator triangulator = new Triangulator(outline.ToArray());
            int[] indices = triangulator.Triangulate();

            if (indices == null || indices.Length == 0)
            {
                Debug.LogError($"拼块 ({x}, {y}) 三角剖分失败");
                return;
            }

            // 创建顶点和UV
            Vector3[] vertices = new Vector3[outline.Count];
            Vector2[] uvs = new Vector2[outline.Count];

            CalculateBounds();
            float width = maxX - minX;
            float height = maxY - minY;

            for (int i = 0; i < outline.Count; i++)
            {
                vertices[i] = new Vector3(outline[i].x, outline[i].y, 0);

                // 计算UV坐标 (0-1范围)
                // 基于整个拼图的坐标系，而不是单个拼块的边界
                if (puzzleWidth > 0 && puzzleHeight > 0)
                {
                    uvs[i] = new Vector2(
                        outline[i].x / puzzleWidth,
                        outline[i].y / puzzleHeight
                    );
                }
                else
                {
                    // 如果没有设置拼图尺寸，回退到原来的计算方式
                    uvs[i] = new Vector2(
                        (outline[i].x - minX) / width,
                        (outline[i].y - minY) / height
                    );
                }
            }

            // 设置网格数据
            mesh.vertices = vertices;
            mesh.triangles = indices;
            mesh.uv = uvs;

            // 重新计算法线和边界
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();
        }
    }
}
