﻿using UnityEngine;

public class PoolItem : MonoBehaviour
{
    internal CustomPool owner;

    public virtual void OnSpawnFromPool() 
    {
        gameObject.SetActive(true);
    }
    public virtual void OnReleaseToPool() 
    {
        gameObject.SetActive(false);
    }

    public void DestroyFromPool()
    {
        if (this == null)
            return;
        GameObject.Destroy(gameObject);
    }

    public void Release()
    {
        if (this == null)
            return;
        owner.Release(this);
    }

    private void OnDestroy()
    {

    }
}