
using UnityEngine;

public class GuideStepDelay : GuideStep
{
    private bool isDone = false;
    private float delay;
    public GuideStepDelay(float delay)
    {
        this.delay = delay;
    }

    private float curTime = 0;
    public override void Update()
    {
        curTime += Time.deltaTime;
        if (curTime >= delay)
        {
            isDone = true;
        }
    }

    public override bool CheckIfHappened()
    {
        return isDone;
    }
}