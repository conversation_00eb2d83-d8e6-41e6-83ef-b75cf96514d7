using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace JigsawGenerator
{
    // 坐标系统说明：
    // - SVG坐标系：(0,0)在左上角，y向下增加
    // - Unity坐标系：(0,0)在左下角，y向上增加
    // - 本文件中的拼图片坐标已修正为Unity坐标系，与GridSystem保持一致

    // 边缘类型枚举
    public enum EdgeType
    {
        Top,
        Right,
        Bottom,
        Left
    }

    public static class JigsawMeshGenerator
    {
        // 从SVG数据生成拼图片段
        public static List<JigsawPiece> GenerateJigsawPieces(string svgData, int tilesX, int tilesY)
        {
            List<JigsawPiece> pieces = new List<JigsawPiece>();

            // 提取SVG的宽度和高度
            float width = 0, height = 0;
            ExtractSVGDimensions(svgData, out width, out height);

            if (width <= 0 || height <= 0)
            {
                Debug.LogError("无法从SVG中提取尺寸信息");
                return pieces;
            }

            // 提取路径数据
            string horizontalPathData = ExtractPathData(svgData, 0);
            string verticalPathData = ExtractPathData(svgData, 1);
            string borderPathData = ExtractPathData(svgData, 2);

            if (string.IsNullOrEmpty(horizontalPathData) ||
                string.IsNullOrEmpty(verticalPathData) ||
                string.IsNullOrEmpty(borderPathData))
            {
                Debug.LogError("无法从SVG中提取路径数据");
                return pieces;
            }

            // 解析路径数据
            List<List<Vector2>> horizontalPaths = SVGParser.ParseSVGPath(horizontalPathData);
            List<List<Vector2>> verticalPaths = SVGParser.ParseSVGPath(verticalPathData);
            List<List<Vector2>> borderPaths = SVGParser.ParseSVGPath(borderPathData);

            if (horizontalPaths.Count == 0 || verticalPaths.Count == 0 || borderPaths.Count == 0)
            {
                Debug.LogError("解析SVG路径数据失败");
                return pieces;
            }

            // 创建拼图片段
            // 初始化拼图片
            for (int y = 0; y < tilesY; y++)
            {
                for (int x = 0; x < tilesX; x++)
                {
                    JigsawPiece piece = new JigsawPiece(x, y);
                    // 设置整个拼图的尺寸信息
                    piece.puzzleWidth = width;
                    piece.puzzleHeight = height;
                    pieces.Add(piece);
                }
            }

            // 计算每个拼图片的边界
            float pieceWidth = width / tilesX;
            float pieceHeight = height / tilesY;

            // 为每个拼图片生成轮廓
            // 修改坐标系统以与GridSystem一致：(0,0)在左下角，y向上增加
            for (int y = 0; y < tilesY; y++)
            {
                for (int x = 0; x < tilesX; x++)
                {
                    // 计算在Unity坐标系中的索引：从底部开始计算
                    int unityY = tilesY - 1 - y; // 将SVG坐标系的y转换为Unity坐标系
                    int pieceIndex = unityY * tilesX + x; // 使用Unity坐标系计算索引

                    JigsawPiece piece = pieces[pieceIndex];

                    // 设置拼图片的正确坐标（Unity坐标系）
                    piece.x = x;
                    piece.y = unityY;

                    List<Vector2> outline = new List<Vector2>();

                    // 构建拼图片的轮廓
                    // 我们需要按顺时针方向添加点，以确保正确的三角剖分

                    // 1. 获取四条边的点（使用SVG坐标系的y进行路径查找）
                    List<Vector2> topEdge = GetEdgePoints(horizontalPaths, x, y, pieceWidth, pieceHeight, EdgeType.Top, tilesX, tilesY);
                    List<Vector2> rightEdge = GetEdgePoints(verticalPaths, x, y, pieceWidth, pieceHeight, EdgeType.Right, tilesX, tilesY);
                    List<Vector2> bottomEdge = GetEdgePoints(horizontalPaths, x, y, pieceWidth, pieceHeight, EdgeType.Bottom, tilesX, tilesY);
                    List<Vector2> leftEdge = GetEdgePoints(verticalPaths, x, y, pieceWidth, pieceHeight, EdgeType.Left, tilesX, tilesY);

                    // 2. 按顺时针方向添加点
                    outline.AddRange(topEdge);
                    outline.AddRange(rightEdge);
                    outline.AddRange(bottomEdge);
                    outline.AddRange(leftEdge);

                    // 3. 确保轮廓是闭合的
                    if (outline.Count > 0 && outline[0] != outline[outline.Count - 1])
                    {
                        outline.Add(outline[0]);
                    }

                    // 4. 设置拼图片的轮廓
                    piece.outline = outline;

                    // 5. 生成网格
                    piece.GenerateMesh();

                    // 调试信息
                    if (piece.mesh == null)
                    {
                        Debug.LogError($"拼块 ({piece.x}, {piece.y}) 网格生成失败，轮廓点数: {piece.outline.Count}");
                    }
                    else
                    {
                        Debug.Log($"拼块 ({piece.x}, {piece.y}) 网格生成成功，顶点数: {piece.mesh.vertexCount}");
                    }
                }
            }

            Debug.Log($"总共生成了 {pieces.Count} 个拼块");
            return pieces;
        }

        // 获取边缘点
        private static List<Vector2> GetEdgePoints(List<List<Vector2>> paths, int x, int y, float pieceWidth, float pieceHeight,
                                                 EdgeType edgeType, int tilesX, int tilesY)
        {
            List<Vector2> edgePoints = new List<Vector2>();

            // 根据边缘类型确定起点和终点
            Vector2 startPoint, endPoint;
            int pathIndex;
            bool needReverse = false;

            switch (edgeType)
            {
                case EdgeType.Top:
                    startPoint = new Vector2(x * pieceWidth, y * pieceHeight);
                    endPoint = new Vector2((x + 1) * pieceWidth, y * pieceHeight);
                    pathIndex = y - 1;

                    // 如果是第一行，直接添加直线
                    if (y == 0)
                    {
                        edgePoints.Add(startPoint);
                        edgePoints.Add(endPoint);
                        return edgePoints;
                    }

                    // 如果是奇数行，需要反转
                    needReverse = (y % 2 == 1);
                    break;

                case EdgeType.Right:
                    startPoint = new Vector2((x + 1) * pieceWidth, y * pieceHeight);
                    endPoint = new Vector2((x + 1) * pieceWidth, (y + 1) * pieceHeight);
                    pathIndex = x;

                    // 如果是最后一列，直接添加直线
                    if (x == tilesX - 1)
                    {
                        edgePoints.Add(startPoint);
                        edgePoints.Add(endPoint);
                        return edgePoints;
                    }

                    // 如果是偶数列，需要反转
                    needReverse = (x % 2 == 0);
                    break;

                case EdgeType.Bottom:
                    startPoint = new Vector2((x + 1) * pieceWidth, (y + 1) * pieceHeight);
                    endPoint = new Vector2(x * pieceWidth, (y + 1) * pieceHeight);
                    pathIndex = y;

                    // 如果是最后一行，直接添加直线
                    if (y == tilesY - 1)
                    {
                        edgePoints.Add(startPoint);
                        edgePoints.Add(endPoint);
                        return edgePoints;
                    }

                    // 底边总是需要反转
                    needReverse = true;
                    break;

                case EdgeType.Left:
                    startPoint = new Vector2(x * pieceWidth, (y + 1) * pieceHeight);
                    endPoint = new Vector2(x * pieceWidth, y * pieceHeight);
                    pathIndex = x - 1;

                    // 如果是第一列，直接添加直线
                    if (x == 0)
                    {
                        edgePoints.Add(startPoint);
                        edgePoints.Add(endPoint);
                        return edgePoints;
                    }

                    // 如果是奇数列，需要反转
                    needReverse = (x % 2 == 1);
                    break;

                default:
                    return edgePoints;
            }

            // 检查路径索引是否有效
            if (pathIndex < 0 || pathIndex >= paths.Count)
            {
                // 如果路径不存在，添加直线
                edgePoints.Add(startPoint);
                edgePoints.Add(endPoint);
                return edgePoints;
            }

            // 获取路径
            List<Vector2> path = paths[pathIndex];

            // 找到属于当前拼图片的部分
            List<Vector2> filteredPoints = new List<Vector2>();

            if (edgeType == EdgeType.Top || edgeType == EdgeType.Bottom)
            {
                // 水平边缘
                foreach (Vector2 point in path)
                {
                    if (point.x >= x * pieceWidth && point.x <= (x + 1) * pieceWidth)
                    {
                        filteredPoints.Add(point);
                    }
                }
            }
            else
            {
                // 垂直边缘
                foreach (Vector2 point in path)
                {
                    if (point.y >= y * pieceHeight && point.y <= (y + 1) * pieceHeight)
                    {
                        filteredPoints.Add(point);
                    }
                }
            }

            // 如果没有找到点，添加直线
            if (filteredPoints.Count == 0)
            {
                edgePoints.Add(startPoint);
                edgePoints.Add(endPoint);
                return edgePoints;
            }

            // 确保第一个点是起点，最后一个点是终点
            if (Vector2.Distance(filteredPoints[0], startPoint) > 0.01f)
            {
                filteredPoints.Insert(0, startPoint);
            }

            if (Vector2.Distance(filteredPoints[filteredPoints.Count - 1], endPoint) > 0.01f)
            {
                filteredPoints.Add(endPoint);
            }

            // 如果需要反转，反转点的顺序
            if (needReverse)
            {
                filteredPoints.Reverse();
            }

            return filteredPoints;
        }

        // 从SVG数据中提取尺寸信息
        private static void ExtractSVGDimensions(string svgData, out float width, out float height)
        {
            width = 0;
            height = 0;

            // 使用正则表达式提取宽度和高度
            System.Text.RegularExpressions.Match widthMatch = System.Text.RegularExpressions.Regex.Match(
                svgData,
                "width=\"([0-9.]+)mm\""
            );

            System.Text.RegularExpressions.Match heightMatch = System.Text.RegularExpressions.Regex.Match(
                svgData,
                "height=\"([0-9.]+)mm\""
            );

            if (widthMatch.Success && widthMatch.Groups.Count > 1)
            {
                float.TryParse(widthMatch.Groups[1].Value, out width);
            }

            if (heightMatch.Success && heightMatch.Groups.Count > 1)
            {
                float.TryParse(heightMatch.Groups[1].Value, out height);
            }
        }

        // 从SVG数据中提取路径数据
        private static string ExtractPathData(string svgData, int pathIndex)
        {
            // 使用正则表达式提取路径数据
            System.Text.RegularExpressions.Match match = System.Text.RegularExpressions.Regex.Match(
                svgData,
                "<path[^>]*d=\"([^\"]*)\"[^>]*>"
            );

            int currentIndex = 0;
            while (match.Success)
            {
                if (currentIndex == pathIndex)
                {
                    return match.Groups[1].Value;
                }

                currentIndex++;
                match = match.NextMatch();
            }

            return null;
        }

        // 创建拼图Prefab
        public static void CreateJigsawPrefab(List<JigsawPiece> pieces, string prefabPath, Texture2D texture)
        {
            // 创建父对象
            GameObject jigsawParent = new GameObject("JigsawPuzzle");

            // 为每个拼图片创建GameObject
            foreach (JigsawPiece piece in pieces)
            {
                // 使用Unity坐标系命名：x_y格式，其中y=0在底部
                GameObject pieceObj = new GameObject($"Piece_{piece.x}_{piece.y}");
                pieceObj.transform.SetParent(jigsawParent.transform);

                // 添加MeshFilter和MeshRenderer组件
                MeshFilter meshFilter = pieceObj.AddComponent<MeshFilter>();
                MeshRenderer meshRenderer = pieceObj.AddComponent<MeshRenderer>();

                // 使用Unity内置的Quad网格
                meshFilter.sharedMesh = CreateQuadMesh();

                // 为这个拼块创建纹理
                Texture2D pieceTexture = CreatePieceTexture(piece, texture);

                // 创建材质
                Shader standardShader = Shader.Find("Standard");
                if (standardShader == null)
                {
                    Debug.LogError("找不到Standard着色器，尝试使用默认着色器");
                    standardShader = Shader.Find("Legacy Shaders/Diffuse");
                }

                if (standardShader == null)
                {
                    Debug.LogError("找不到任何可用的着色器");
                    continue;
                }

                Material material = new Material(standardShader);
                material.mainTexture = pieceTexture;

                meshRenderer.sharedMaterial = material;

                // 添加碰撞器 - 使用BoxCollider更简单
                BoxCollider collider = pieceObj.AddComponent<BoxCollider>();
                collider.size = new Vector3(1, 1, 0.1f); // 设置碰撞器大小

                // 添加Rigidbody组件使拼图片可以移动
                Rigidbody rb = pieceObj.AddComponent<Rigidbody>();
                rb.useGravity = false; // 禁用重力，使拼图片在平面上移动
                rb.constraints = RigidbodyConstraints.FreezePositionZ |
                                RigidbodyConstraints.FreezeRotationX |
                                RigidbodyConstraints.FreezeRotationY;
            }

            // 创建Prefab
            string directory = Path.GetDirectoryName(prefabPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

#if UNITY_2018_3_OR_NEWER
            PrefabUtility.SaveAsPrefabAsset(jigsawParent, prefabPath);
#else
            PrefabUtility.CreatePrefab(prefabPath, jigsawParent);
#endif

            // 删除场景中的临时对象
            Object.DestroyImmediate(jigsawParent);

            Debug.Log("拼图Prefab已创建: " + prefabPath);
        }

        // 创建Quad网格
        private static Mesh CreateQuadMesh()
        {
            Mesh mesh = new Mesh();

            // 顶点 (1x1的四边形，中心在原点)
            Vector3[] vertices = new Vector3[]
            {
                new Vector3(-0.5f, -0.5f, 0),  // 左下
                new Vector3(0.5f, -0.5f, 0),   // 右下
                new Vector3(0.5f, 0.5f, 0),    // 右上
                new Vector3(-0.5f, 0.5f, 0)    // 左上
            };

            // UV坐标
            Vector2[] uvs = new Vector2[]
            {
                new Vector2(0, 0),  // 左下
                new Vector2(1, 0),  // 右下
                new Vector2(1, 1),  // 右上
                new Vector2(0, 1)   // 左上
            };

            // 三角形索引
            int[] triangles = new int[]
            {
                0, 1, 2,  // 第一个三角形
                0, 2, 3   // 第二个三角形
            };

            mesh.vertices = vertices;
            mesh.uv = uvs;
            mesh.triangles = triangles;
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();

            return mesh;
        }

        // 为拼块创建纹理
        private static Texture2D CreatePieceTexture(JigsawPiece piece, Texture2D sourceTexture)
        {
            int textureSize = 512; // 纹理大小
            Texture2D pieceTexture = new Texture2D(textureSize, textureSize, TextureFormat.RGBA32, false);

            // 清空纹理为透明
            Color[] clearColors = new Color[textureSize * textureSize];
            for (int i = 0; i < clearColors.Length; i++)
            {
                clearColors[i] = Color.clear;
            }
            pieceTexture.SetPixels(clearColors);

            if (piece.outline.Count < 3)
            {
                Debug.LogError($"拼块 ({piece.x}, {piece.y}) 轮廓点数不足");
                return pieceTexture;
            }

            // 计算拼块的边界
            piece.CalculateBounds();
            float pieceWidth = piece.maxX - piece.minX;
            float pieceHeight = piece.maxY - piece.minY;

            if (pieceWidth <= 0 || pieceHeight <= 0)
            {
                Debug.LogError($"拼块 ({piece.x}, {piece.y}) 尺寸无效");
                return pieceTexture;
            }

            // 计算缩放比例，确保拼块适合纹理
            float scale = Mathf.Min(textureSize * 0.8f / pieceWidth, textureSize * 0.8f / pieceHeight);

            // 计算偏移，使拼块居中
            float offsetX = (textureSize - pieceWidth * scale) / 2 - piece.minX * scale;
            float offsetY = (textureSize - pieceHeight * scale) / 2 - piece.minY * scale;

            // 绘制拼块轮廓
            DrawPieceOutline(pieceTexture, piece.outline, scale, offsetX, offsetY, sourceTexture, piece);

            pieceTexture.Apply();
            return pieceTexture;
        }

        // 绘制拼块轮廓到纹理上
        private static void DrawPieceOutline(Texture2D texture, List<Vector2> outline, float scale, float offsetX, float offsetY, Texture2D sourceTexture, JigsawPiece piece)
        {
            int textureSize = texture.width;

            // 使用简单的扫描线算法填充拼块
            for (int y = 0; y < textureSize; y++)
            {
                for (int x = 0; x < textureSize; x++)
                {
                    // 将纹理坐标转换为世界坐标
                    float worldX = (x - offsetX) / scale;
                    float worldY = (y - offsetY) / scale;

                    // 检查点是否在拼块内部
                    if (IsPointInPolygon(new Vector2(worldX, worldY), outline))
                    {
                        Color pixelColor = Color.white; // 默认白色

                        // 如果有源纹理，从源纹理采样
                        if (sourceTexture != null && piece.puzzleWidth > 0 && piece.puzzleHeight > 0)
                        {
                            float uvX = worldX / piece.puzzleWidth;
                            float uvY = worldY / piece.puzzleHeight;

                            // 确保UV坐标在有效范围内
                            uvX = Mathf.Clamp01(uvX);
                            uvY = Mathf.Clamp01(uvY);

                            // 从源纹理采样
                            int sourceX = Mathf.FloorToInt(uvX * (sourceTexture.width - 1));
                            int sourceY = Mathf.FloorToInt(uvY * (sourceTexture.height - 1));
                            pixelColor = sourceTexture.GetPixel(sourceX, sourceY);
                        }

                        texture.SetPixel(x, y, pixelColor);
                    }
                }
            }
        }

        // 检查点是否在多边形内部（射线投射算法）
        private static bool IsPointInPolygon(Vector2 point, List<Vector2> polygon)
        {
            int count = polygon.Count;
            if (count < 3) return false;

            bool inside = false;
            int j = count - 1;

            for (int i = 0; i < count; i++)
            {
                Vector2 pi = polygon[i];
                Vector2 pj = polygon[j];

                if (((pi.y > point.y) != (pj.y > point.y)) &&
                    (point.x < (pj.x - pi.x) * (point.y - pi.y) / (pj.y - pi.y) + pi.x))
                {
                    inside = !inside;
                }
                j = i;
            }

            return inside;
        }
    }
}
