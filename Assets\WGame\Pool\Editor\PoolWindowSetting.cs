﻿using System;
using System.Collections;
using UnityEditor;
using UnityEngine;

namespace PoolEditor
{
    public class PoolWindowSetting : ScriptableObject
    {
        public PoolSetting[] settings;
    }

    [Serializable]
    public class PoolSetting
    {
        public DefaultAsset poolItemFolder;
        public PoolSettingData pool;
        public int initCount = 10;
        public int maxCount = 100;
    }
}