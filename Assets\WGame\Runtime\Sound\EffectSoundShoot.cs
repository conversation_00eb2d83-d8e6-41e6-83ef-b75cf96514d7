﻿using System.Collections.Generic;
using UnityEngine;

public class EffectSoundShoot : MonoBehaviour {

	private string path;
	private AudioSource audioSource;
	private float startTime;
	public float duration = float.MaxValue;
    private bool isStartPlay;

	void Awake()
	{
		audioSource = gameObject.AddComponent<AudioSource>();
	}

    public float volume
    {
        set
        {
            audioSource.volume = value;
        }
        get
        {
            return audioSource.volume;
        }
    }

    public void Play()
    {
        isStartPlay = true;
        audioSource.Play();
        startTime = Time.unscaledTime;
	}
    
	// Update is called once per frame
	void FixedUpdate () 
	{
        if (!isStartPlay)
            return;

		if(audioSource == null || !audioSource.isPlaying || Time.unscaledTime - startTime >= duration)
		{
            Remove();
        }
	}
    public void SetPitch(float value)
    {
        audioSource.pitch = value;
    }
    public void Remove()
    {
        audioSource.Stop();
        gameObject.SetActive(false);
        isStartPlay = false;
        existList.Remove(this);
        Recycle(this);
    }


    private static Dictionary<string, List<EffectSoundShoot>> cacheDict = new Dictionary<string, List<EffectSoundShoot>>();
	private static Dictionary<string, float> shootTimeDict = new Dictionary<string, float> ();
    private static List<EffectSoundShoot> existList = new List<EffectSoundShoot>();

    public static EffectSoundShoot Create(string path, float volume, Vector3 pos, float duration = float.MaxValue, bool loop = false, float pitch = 1f)
    {
        // float time = 0;
        // if (!shootTimeDict.TryGetValue (path, out time))
        // {
        // 	shootTimeDict.Add (path, Time.unscaledTime);
        // }
        // else
        // {
        // 	if(Time.unscaledTime - time < 0.1f)
        // 	{
        // 		return null;
        // 	}
        // 	shootTimeDict [path] = Time.unscaledTime;
        // }

        List<EffectSoundShoot> list = null;
        if (!cacheDict.TryGetValue(path, out list))
		{
			list = new List<EffectSoundShoot> ();
			cacheDict.Add (path, list);
		}

		EffectSoundShoot shoot = null;

        if (list.Count > 0)
		{
			shoot = list [0];
			shoot.gameObject.SetActive (true);
            shoot.transform.position = pos;
            shoot.SetPitch(pitch);
            shoot.Play();
            shoot.volume = volume;
            list.RemoveAt (0);
            existList.Add(shoot);
            return shoot;
		}

        if (volume.Equals(0))
            return null;

        GameObject soundObj = new GameObject();
        soundObj.transform.SetParent(SoundManager.GetInstatnce().transform);
        soundObj.name = path;
		shoot = soundObj.AddComponent<EffectSoundShoot>();
		shoot.audioSource.volume = volume;
        shoot.audioSource.spatialBlend = pos.Equals(Vector3.zero) ? 0f : 1f;
        shoot.duration = duration;
        shoot.SetPitch(pitch);
        SoundManager.LoadEffect(shoot.audioSource, path, (bool success) => { shoot.Play(); });
        shoot.path = path;
        shoot.transform.position = pos;
        shoot.audioSource.loop = loop;
        existList.Add(shoot);
        return shoot;
    }


	public static void Stop(string path)
    {
        for(int i=existList.Count-1; i>=0; i--)
        {
            EffectSoundShoot shoot = existList[i];
            if(shoot.path.Equals(path))
            {
                shoot.Remove();
            }
        }
    }

    public static void StopAll()
    {
        for (int i = existList.Count - 1; i >= 0; i--)
        {
            EffectSoundShoot shoot = existList[i];
            shoot.Remove();
        }
    }


    public static void Recycle(EffectSoundShoot shoot)
	{
		List<EffectSoundShoot> list = null;
		if (cacheDict.TryGetValue(shoot.path, out list))
		{
			list.Add (shoot);
		}
	}
}
