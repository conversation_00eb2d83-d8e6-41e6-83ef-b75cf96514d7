using System.IO;
using UnityEditor;
using UnityEngine;


namespace PoolEditor
{
    public class PoolWindow : EditorWindow
    {
        private static PoolWindowSetting _setting;
        public static PoolWindowSetting Setting
        {
            get
            {
                if (_setting == null)
                {
                    var settingPath = Path.Combine("Assets", "WGame", "EasyPoolSetting.asset");
                    if (File.Exists(settingPath))
                    {
                        _setting = AssetDatabase.LoadAssetAtPath<PoolWindowSetting>(settingPath);
                    }
                    else
                    {
                        _setting = ScriptableObject.CreateInstance<PoolWindowSetting>();
                        AssetDatabase.CreateAsset(_setting, settingPath);
                    }
                }
                return _setting;
            }
        }


        [MenuItem("Tools/EasyPool", priority = 1)]
        static void AddWindow()
        {
            PoolWindow window = EditorWindow.GetWindow<PoolWindow>(false, "EasyPool 1.0");

            window.setting = Setting;
            window.settingSo = new SerializedObject(window.setting);
            window.settingEditor = Editor.CreateEditor(window.setting);

            window.Show();
        }

        public static void RefreshPoolData()
        {
            UpdatePoolData();
            EditorUtility.SetDirty(Setting);
            AssetDatabase.SaveAssets();
            Debug.Log("====刷新对象池资源====");
        }

        [SerializeField]
        private PoolWindowSetting setting;
        private SerializedObject settingSo;
        private Editor settingEditor;
        private Vector2 scrollPosition;
        private void OnGUI()
        {
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            settingEditor.OnInspectorGUI();
            EditorGUILayout.EndScrollView();
            if (GUILayout.Button("更新", GUILayout.Height(40)))
            {
                RefreshPoolData();
            }
        }

        private static void UpdatePoolData()
        {
            var setting = Setting;
            foreach (var poolSetting in setting.settings)
            {
                if (poolSetting.poolItemFolder == null || poolSetting.pool == null)
                {
                    continue;
                }
                var path = AssetDatabase.GetAssetPath(poolSetting.poolItemFolder);
                var files = Directory.GetFiles(path, "*.prefab", SearchOption.AllDirectories);
                poolSetting.pool.poolDatas = new PoolData[files.Length];
                for (int i = 0; i < files.Length; i++)
                {
                    var file = files[i];
                    var uuid = AssetDatabase.AssetPathToGUID(file);
                    var poolItem = new PoolData();
                    poolItem.prefabRef = AssetDatabase.LoadAssetAtPath<GameObject>(file);
                    poolItem.initCount = poolSetting.initCount;
                    poolItem.maxCount = poolSetting.maxCount;
                    poolSetting.pool.poolDatas[i] = poolItem;
                }
                EditorUtility.SetDirty(poolSetting.pool);
            }
        }
    }
}
