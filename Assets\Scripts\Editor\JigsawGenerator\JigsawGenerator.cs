using UnityEngine;
using System;
using System.Text;
using System.Collections.Generic;

namespace JigsawGenerator
{
    // 坐标系统说明：
    // - 内部生成使用SVG坐标系：(0,0)在左上角，y向下增加
    // - 输出文件名使用Unity坐标系：(0,0)在左下角，y向上增加，与GridSystem保持一致

    // 分割线段数据结构
    public class DividerSegment
    {
        public string pathData;
        public string reversedPathData; // 反向路径数据
        public int x, y; // 分割线的位置
    }

    public static class JigsawGenerator
    {
        // 拼图生成参数
        private static int seed;
        private static float tabSize;
        private static float jitter;
        private static float cornerRadius;
        private static int tilesX;
        private static int tilesY;
        private static float width;
        private static float height;
        private static float offset;


        // 临时变量，对应原JavaScript代码中的变量
        private static float a, b, c, d, e, t, j, xi, yi, xn, yn;
        private static bool flip, vertical;

        // 随机数生成
        private static float Random()
        {
            float x = Mathf.Sin(seed) * 10000;
            seed += 1;
            return x - Mathf.Floor(x);
        }

        private static float Uniform(float min, float max)
        {
            float r = Random();
            return min + r * (max - min);
        }

        private static bool RBool()
        {
            return Random() > 0.5f;
        }

        // 初始化参数
        private static void InitParams(int seedValue, float tabSizeValue, float jitterValue, float cornerRadiusValue,
                                      int tilesXValue, int tilesYValue, float widthValue, float heightValue)
        {
            seed = seedValue;
            tabSize = tabSizeValue / 200.0f; // 转换为0-1范围内的值
            jitter = jitterValue / 100.0f;   // 转换为0-1范围内的值
            cornerRadius = cornerRadiusValue;
            tilesX = tilesXValue;
            tilesY = tilesYValue;
            width = widthValue;
            height = heightValue;
            offset = 0.0f;

            // 初始化临时变量
            t = tabSize;
            j = jitter;
            xn = tilesX;
            yn = tilesY;
        }

        // 曲线生成函数
        private static void First()
        {
            e = Uniform(-j, j);
            Next();
        }

        private static void Next()
        {
            bool flipOld = flip;
            flip = RBool();
            a = (flip == flipOld ? -e : e);
            b = Uniform(-j, j);
            c = Uniform(-j, j);
            d = Uniform(-j, j);
            e = Uniform(-j, j);
        }

        // 计算坐标函数
        private static float Sl()
        {
            return vertical ? height / yn : width / xn;
        }

        private static float Sw()
        {
            return vertical ? width / xn : height / yn;
        }

        private static float Ol()
        {
            return offset + Sl() * (vertical ? yi : xi);
        }

        private static float Ow()
        {
            return offset + Sw() * (vertical ? xi : yi);
        }

        private static float L(float v)
        {
            float ret = Ol() + Sl() * v;
            return Mathf.Round(ret * 100) / 100;
        }

        private static float W(float v)
        {
            float ret = Ow() + Sw() * v * (flip ? -1.0f : 1.0f);
            return Mathf.Round(ret * 100) / 100;
        }

        // 控制点计算
        private static float P0l() { return L(0.0f); }
        private static float P0w() { return W(0.0f); }
        private static float P1l() { return L(0.2f); }
        private static float P1w() { return W(a); }
        private static float P2l() { return L(0.5f + b + d); }
        private static float P2w() { return W(-t + c); }
        private static float P3l() { return L(0.5f - t + b); }
        private static float P3w() { return W(t + c); }
        private static float P4l() { return L(0.5f - 2.0f * t + b - d); }
        private static float P4w() { return W(3.0f * t + c); }
        private static float P5l() { return L(0.5f + 2.0f * t + b - d); }
        private static float P5w() { return W(3.0f * t + c); }
        private static float P6l() { return L(0.5f + t + b); }
        private static float P6w() { return W(t + c); }
        private static float P7l() { return L(0.5f + b + d); }
        private static float P7w() { return W(-t + c); }
        private static float P8l() { return L(0.8f); }
        private static float P8w() { return W(e); }
        private static float P9l() { return L(1.0f); }
        private static float P9w() { return W(0.0f); }

        // 生成水平分割线
        private static string GenerateHorizontalDividers()
        {
            StringBuilder str = new StringBuilder();
            vertical = false;

            for (yi = 1; yi < yn; ++yi)
            {
                xi = 0;
                First();
                str.Append($"M {P0l()},{P0w()} ");

                for (; xi < xn; ++xi)
                {
                    str.Append($"C {P1l()} {P1w()} {P2l()} {P2w()} {P3l()} {P3w()} ");
                    str.Append($"C {P4l()} {P4w()} {P5l()} {P5w()} {P6l()} {P6w()} ");
                    str.Append($"C {P7l()} {P7w()} {P8l()} {P8w()} {P9l()} {P9w()} ");
                    Next();
                }
            }

            return str.ToString();
        }

        // 生成垂直分割线
        private static string GenerateVerticalDividers()
        {
            StringBuilder str = new StringBuilder();
            vertical = true;

            for (xi = 1; xi < xn; ++xi)
            {
                yi = 0;
                First();
                str.Append($"M {P0w()},{P0l()} ");

                for (; yi < yn; ++yi)
                {
                    str.Append($"C {P1w()} {P1l()} {P2w()} {P2l()} {P3w()} {P3l()} ");
                    str.Append($"C {P4w()} {P4l()} {P5w()} {P5l()} {P6w()} {P6l()} ");
                    str.Append($"C {P7w()} {P7l()} {P8w()} {P8l()} {P9w()} {P9l()} ");
                    Next();
                }
            }

            return str.ToString();
        }

        // 生成边界
        private static string GenerateBorder()
        {
            StringBuilder str = new StringBuilder();

            str.Append($"M {(offset + cornerRadius)} {(offset)} ");
            str.Append($"L {(offset + width - cornerRadius)} {(offset)} ");
            str.Append($"A {(cornerRadius)} {(cornerRadius)} 0 0 1 {(offset + width)} {(offset + cornerRadius)} ");
            str.Append($"L {(offset + width)} {(offset + height - cornerRadius)} ");
            str.Append($"A {(cornerRadius)} {(cornerRadius)} 0 0 1 {(offset + width - cornerRadius)} {(offset + height)} ");
            str.Append($"L {(offset + cornerRadius)} {(offset + height)} ");
            str.Append($"A {(cornerRadius)} {(cornerRadius)} 0 0 1 {(offset)} {(offset + height - cornerRadius)} ");
            str.Append($"L {(offset)} {(offset + cornerRadius)} ");
            str.Append($"A {(cornerRadius)} {(cornerRadius)} 0 0 1 {(offset + cornerRadius)} {(offset)} ");

            return str.ToString();
        }

        // 生成水平分割线数据（每个分割线段单独存储）
        private static List<DividerSegment> GenerateHorizontalDividersData()
        {
            List<DividerSegment> segments = new List<DividerSegment>();
            vertical = false;

            for (yi = 1; yi < yn; ++yi)
            {
                xi = 0;
                First();  // 与GenerateHorizontalDividers()保持一致

                for (; xi < xn; ++xi)
                {
                    string segmentData = $"C {P1l()} {P1w()} {P2l()} {P2w()} {P3l()} {P3w()} " +
                                       $"C {P4l()} {P4w()} {P5l()} {P5w()} {P6l()} {P6w()} " +
                                       $"C {P7l()} {P7w()} {P8l()} {P8w()} {P9l()} {P9w()} ";

                    // 生成反向路径数据
                    string reversedData = $"C {P8l()} {P8w()} {P7l()} {P7w()} {P6l()} {P6w()} " +
                                        $"C {P5l()} {P5w()} {P4l()} {P4w()} {P3l()} {P3w()} " +
                                        $"C {P2l()} {P2w()} {P1l()} {P1w()} {P0l()} {P0w()} ";

                    segments.Add(new DividerSegment
                    {
                        pathData = segmentData,
                        reversedPathData = reversedData,
                        x = (int)xi,
                        y = (int)yi
                    });

                    Next();  // 与GenerateHorizontalDividers()保持一致
                }
            }

            return segments;
        }

        // 生成垂直分割线数据（每个分割线段单独存储）
        private static List<DividerSegment> GenerateVerticalDividersData()
        {
            List<DividerSegment> segments = new List<DividerSegment>();
            vertical = true;

            for (xi = 1; xi < xn; ++xi)
            {
                yi = 0;
                First();  // 与GenerateVerticalDividers()保持一致

                for (; yi < yn; ++yi)
                {
                    string segmentData = $"C {P1w()} {P1l()} {P2w()} {P2l()} {P3w()} {P3l()} " +
                                       $"C {P4w()} {P4l()} {P5w()} {P5l()} {P6w()} {P6l()} " +
                                       $"C {P7w()} {P7l()} {P8w()} {P8l()} {P9w()} {P9l()} ";

                    // 生成反向路径数据
                    string reversedData = $"C {P8w()} {P8l()} {P7w()} {P7l()} {P6w()} {P6l()} " +
                                        $"C {P5w()} {P5l()} {P4w()} {P4l()} {P3w()} {P3l()} " +
                                        $"C {P2w()} {P2l()} {P1w()} {P1l()} {P0w()} {P0l()} ";

                    segments.Add(new DividerSegment
                    {
                        pathData = segmentData,
                        reversedPathData = reversedData,
                        x = (int)xi,
                        y = (int)yi
                    });

                    Next();  // 与GenerateVerticalDividers()保持一致
                }
            }

            return segments;
        }

        // 生成单个拼块的SVG
        private static string GenerateIndividualPiece(int pieceX, int pieceY)
        {
            // 计算拼块的边界
            float pieceWidth = width / tilesX;
            float pieceHeight = height / tilesY;
            float pieceLeft = pieceX * pieceWidth;
            float pieceTop = pieceY * pieceHeight;
            float pieceRight = pieceLeft + pieceWidth;
            float pieceBottom = pieceTop + pieceHeight;

            // 为了确保拼块的凸起部分完全显示，我们需要扩展画布
            // 增加画布尺寸，确保能显示凸起部分，但保持拼块居中
            float canvasWidth = pieceWidth * 1.0f + 80;
            float canvasHeight = pieceHeight * 1.0f + 80;

            // 计算拼块在画布中的居中位置
            float pieceOffsetX = (canvasWidth - pieceWidth) / 2;
            float pieceOffsetY = (canvasHeight - pieceHeight) / 2;

            // 计算画布的左上角坐标，使拼块居中显示
            float canvasLeft = pieceLeft - pieceOffsetX;
            float canvasTop = pieceTop - pieceOffsetY;

            StringBuilder pieceData = new StringBuilder();

            // 添加SVG头部
            pieceData.Append($"<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.0\" ");
            // 设置SVG的宽高为拼块的基本尺寸，但viewBox包含更大区域以显示凸起部分
            pieceData.Append($"width=\"{pieceWidth}mm\" height=\"{pieceHeight}mm\" viewBox=\"{canvasLeft} {canvasTop} {canvasWidth} {canvasHeight}\">");

            // 添加拼块路径
            // pieceData.Append($"<path fill=\"none\" stroke=\"Black\" stroke-width=\"0.1\" d=\"");
            pieceData.Append($"<path fill=\"red\" stroke=\"none\" d=\"");
            // 构建拼块路径 - 从左上角开始，顺时针方向
            // 起始点 - 左上角
            pieceData.Append($"M {pieceLeft} {pieceTop} ");

            // 上边缘 - 从左到右
            if (pieceY == 0)
            {
                // 顶部边缘是直线
                pieceData.Append($"L {pieceRight} {pieceTop} ");
            }
            else
            {
                // 使用水平分割线的一部分
                vertical = false;
                yi = pieceY;
                xi = pieceX;
                First();
                // 不需要再添加起点，因为已经有了M命令
                pieceData.Append($"C {P1l()} {P1w()} {P2l()} {P2w()} {P3l()} {P3w()} ");
                pieceData.Append($"C {P4l()} {P4w()} {P5l()} {P5w()} {P6l()} {P6w()} ");
                pieceData.Append($"C {P7l()} {P7w()} {P8l()} {P8w()} {P9l()} {P9w()} ");
            }

            // 右边缘 - 从上到下
            if (pieceX == tilesX - 1)
            {
                // 右侧边缘是直线
                pieceData.Append($"L {pieceRight} {pieceBottom} ");
            }
            else
            {
                // 使用垂直分割线的一部分
                vertical = true;
                xi = pieceX + 1;
                yi = pieceY;
                First();
                pieceData.Append($"C {P1w()} {P1l()} {P2w()} {P2l()} {P3w()} {P3l()} ");
                pieceData.Append($"C {P4w()} {P4l()} {P5w()} {P5l()} {P6w()} {P6l()} ");
                pieceData.Append($"C {P7w()} {P7l()} {P8w()} {P8l()} {P9w()} {P9l()} ");
            }

            // 下边缘 - 从右到左
            if (pieceY == tilesY - 1)
            {
                // 底部边缘是直线
                pieceData.Append($"L {pieceLeft} {pieceBottom} ");
            }
            else
            {
                // 使用水平分割线的一部分，但需要反向
                vertical = false;
                yi = pieceY + 1;
                xi = pieceX;
                First();

                // 先移动到右下角
                pieceData.Append($"L {pieceRight} {pieceBottom} ");

                // 然后沿着下边缘反向移动
                pieceData.Append($"C {P8l()} {P8w()} {P7l()} {P7w()} {P6l()} {P6w()} ");
                pieceData.Append($"C {P5l()} {P5w()} {P4l()} {P4w()} {P3l()} {P3w()} ");
                pieceData.Append($"C {P2l()} {P2w()} {P1l()} {P1w()} {P0l()} {P0w()} ");
            }

            // 左边缘 - 从下到上，回到起点
            if (pieceX == 0)
            {
                // 左侧边缘是直线，使用Z命令闭合路径
                pieceData.Append($"L {pieceLeft} {pieceTop} ");
            }
            else
            {
                // 使用垂直分割线的一部分，但需要反向
                vertical = true;
                xi = pieceX;
                yi = pieceY;
                First();

                // 先移动到左下角
                pieceData.Append($"L {pieceLeft} {pieceBottom} ");

                // 然后沿着左边缘向上移动
                pieceData.Append($"C {P8w()} {P8l()} {P7w()} {P7l()} {P6w()} {P6l()} ");
                pieceData.Append($"C {P5w()} {P5l()} {P4w()} {P4l()} {P3w()} {P3l()} ");
                pieceData.Append($"C {P2w()} {P2l()} {P1w()} {P1l()} {P0w()} {P0l()} ");
            }

            pieceData.Append("\"></path>");
            pieceData.Append("</svg>");

            return pieceData.ToString();
        }



        // 从预生成的分割线数据生成单个拼图块的SVG
        private static string GenerateIndividualPieceFromData(int pieceX, int pieceY,
            List<DividerSegment> horizontalDividers, List<DividerSegment> verticalDividers)
        {
            // 计算拼块的边界
            float pieceWidth = width / tilesX;
            float pieceHeight = height / tilesY;
            float pieceLeft = pieceX * pieceWidth;
            float pieceTop = pieceY * pieceHeight;
            float pieceRight = pieceLeft + pieceWidth;
            float pieceBottom = pieceTop + pieceHeight;

            // 为了确保拼块的凸起部分完全显示，我们需要扩展画布
            float canvasWidth = pieceWidth + 80;
            float canvasHeight = pieceHeight + 80;

            // 计算拼块在画布中的居中位置
            float pieceOffsetX = 40;
            float pieceOffsetY = 40;

            // 计算画布的左上角坐标，使拼块居中显示
            float canvasLeft = pieceLeft - pieceOffsetX;
            float canvasTop = pieceTop - pieceOffsetY;

            StringBuilder pieceData = new StringBuilder();

            // 添加SVG头部 - 使用固定的190x190尺寸，与Unity中的拼图块尺寸一致
            pieceData.Append($"<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.0\" ");
            pieceData.Append($"width=\"190\" height=\"190\" viewBox=\"{canvasLeft} {canvasTop} {canvasWidth} {canvasHeight}\">");

            // 添加调试注释
            // pieceData.Append($"<!-- Piece at SVG coordinates ({pieceX}, {pieceY}) -->");
            pieceData.Append($"<path fill=\"red\" stroke=\"none\" d=\"");
            // pieceData.Append($"<path fill=\"red\" stroke=\"black\" stroke-width=\"0.5\" d=\"");

            // 构建完整的拼图块路径
            // 起始点 - 左上角
            pieceData.Append($"M {pieceLeft} {pieceTop} ");

            // 上边缘 - 从左到右
            if (pieceY == 0)
            {
                // SVG坐标系顶部边缘是直线（整个拼图的顶部边界）
                pieceData.Append($"L {pieceRight} {pieceTop} ");
            }
            else
            {
                // 查找对应的水平分割线段
                var segment = horizontalDividers.Find(s => s.x == pieceX && s.y == pieceY);
                if (segment != null)
                {
                    pieceData.Append(segment.pathData);
                }
                else
                {
                    pieceData.Append($"L {pieceRight} {pieceTop} ");
                }
            }

            // 右边缘 - 从上到下
            if (pieceX == tilesX - 1)
            {
                // 右侧边缘是直线（整个拼图的右边界）
                pieceData.Append($"L {pieceRight} {pieceBottom} ");
            }
            else
            {
                // 查找对应的垂直分割线段
                var segment = verticalDividers.Find(s => s.x == pieceX + 1 && s.y == pieceY);
                if (segment != null)
                {
                    pieceData.Append(segment.pathData);
                }
                else
                {
                    pieceData.Append($"L {pieceRight} {pieceBottom} ");
                }
            }

            // 下边缘 - 从右到左
            if (pieceY == tilesY - 1)
            {
                // SVG坐标系底部边缘是直线（整个拼图的底部边界）
                pieceData.Append($"L {pieceLeft} {pieceBottom} ");
            }
            else
            {
                // 查找对应的水平分割线段，使用反向路径数据
                var segment = horizontalDividers.Find(s => s.x == pieceX && s.y == pieceY + 1);
                if (segment != null)
                {
                    pieceData.Append(segment.reversedPathData);
                }
                else
                {
                    // 如果找不到分割线，使用直线
                    pieceData.Append($"L {pieceLeft} {pieceBottom} ");
                }
            }

            // 左边缘 - 从下到上，回到起点
            if (pieceX == 0)
            {
                // 左侧边缘是直线（整个拼图的左边界）
                pieceData.Append($"L {pieceLeft} {pieceTop} ");
            }
            else
            {
                // 查找对应的垂直分割线段，使用反向路径数据
                var segment = verticalDividers.Find(s => s.x == pieceX && s.y == pieceY);
                if (segment != null)
                {
                    pieceData.Append(segment.reversedPathData);
                }
                else
                {
                    // 如果找不到分割线，使用直线
                    pieceData.Append($"L {pieceLeft} {pieceTop} ");
                }
            }

            // 闭合路径
            pieceData.Append("Z");
            pieceData.Append("\"></path>");
            pieceData.Append("</svg>");

            return pieceData.ToString();
        }

        // 生成所有拼块的SVG
        public static Dictionary<string, string> GenerateAllPieces(int seedValue, float tabSizeValue, float jitterValue, float cornerRadiusValue,
                                                                 int tilesXValue, int tilesYValue, float widthValue, float heightValue)
        {
            InitParams(seedValue, tabSizeValue, jitterValue, cornerRadiusValue, tilesXValue, tilesYValue, widthValue, heightValue);

            // 预先生成所有分割线数据
            var horizontalDividers = GenerateHorizontalDividersData();
            var verticalDividers = GenerateVerticalDividersData();

            Dictionary<string, string> piecesData = new Dictionary<string, string>();

            // 生成每个拼块的SVG
            // 注意：统一使用Unity坐标系，确保文件名和拼图形状的坐标系一致
            for (int unityY = 0; unityY < tilesY; unityY++)
            {
                for (int x = 0; x < tilesX; x++)
                {
                    // 转换为SVG坐标系用于生成拼图形状
                    int svgY = tilesY - 1 - unityY;
                    string pieceName = $"piece_{x}_{unityY}.svg";
                    string pieceData = GenerateIndividualPieceFromData(x, svgY, horizontalDividers, verticalDividers);
                    piecesData.Add(pieceName, pieceData);
                }
            }

            return piecesData;
        }

        // 生成SVG
        public static string GenerateSVG(int seedValue, float tabSizeValue, float jitterValue, float cornerRadiusValue,
                                        int tilesXValue, int tilesYValue, float widthValue, float heightValue)
        {
            InitParams(seedValue, tabSizeValue, jitterValue, cornerRadiusValue, tilesXValue, tilesYValue, widthValue, heightValue);

            StringBuilder svgData = new StringBuilder();
            svgData.Append($"<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.0\" ");
            svgData.Append($"width=\"{width}mm\" height=\"{height}mm\" viewBox=\"0 0 {width} {height}\">");

            svgData.Append($"<path fill=\"none\" stroke=\"DarkBlue\" stroke-width=\"0.1\" d=\"");
            svgData.Append(GenerateHorizontalDividers());
            svgData.Append("\"></path>");

            svgData.Append($"<path fill=\"none\" stroke=\"DarkRed\" stroke-width=\"0.1\" d=\"");
            svgData.Append(GenerateVerticalDividers());
            svgData.Append("\"></path>");

            svgData.Append($"<path fill=\"none\" stroke=\"Black\" stroke-width=\"0.1\" d=\"");
            svgData.Append(GenerateBorder());
            svgData.Append("\"></path>");

            svgData.Append("</svg>");

            return svgData.ToString();
        }
    }
}
