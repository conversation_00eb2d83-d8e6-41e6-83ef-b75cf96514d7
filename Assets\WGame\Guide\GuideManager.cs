// using System;
// using System.Collections.Generic;
// using UnityEngine;

// public class GuideManager : MonoBehaviour
// {
//     private static GuideManager _instance;
//     public static GuideManager Inst
//     {
//         get
//         {
//             if (_instance == null)
//             {
//                 var go = new GameObject
//                 {
//                     name = "GuideManager"
//                 };
//                 _instance = go.AddComponent<GuideManager>();
//             }
//             return _instance;
//         }
//     }

//     private Queue<GuideStep> stepsQueue = new Queue<GuideStep>();
//     private GuideStep currentStep;
//     private Action<int> OnStepDone;
//     private GuideComp guideComp;
//     public GuideComp CreateGuideUI(float maskWidth, float maskHeight, float maskAlpha, Vector3 msgOffset)
//     {
//         if (guideComp != null)
//         {
//             Clear();
//         }
//         guideComp = GuideComp.Create(new GuideParam
//         {
//             maskAlpha = maskAlpha,
//             maskWidth = maskWidth,
//             maskHeight = maskHeight,
//             msgOffset = msgOffset
//         });
//         return guideComp;
//     }

//     public void EnqueueStep(GuideStep step, Action<int> OnStepDone = null)
//     {
//         this.OnStepDone = OnStepDone;
//         stepsQueue.Enqueue(step);
//     }

//     public void EnqueueStep(GuideStep[] steps, Action<int> OnStepDone = null)
//     {
//         this.OnStepDone = OnStepDone;
//         for (int i = 0; i < steps.Length; i++)
//         {
//             var step = steps[i];
//             step.stepIndex = i + 1;
//             stepsQueue.Enqueue(step);
//         }
//     }

//     public void Clear()
//     {
//         if (guideComp != null)
//         {
//             guideComp.Hide();
//             guideComp = null;
//         }
//         stepsQueue.Clear();
//         currentStep = null;
//     }

//     private void Update()
//     {
//         if (currentStep == null || currentStep.CheckIfHappened())
//         {
//             if (currentStep != null)
//             {
//                 OnStepDone?.Invoke(currentStep.stepIndex);
//             }

//             if (stepsQueue.Count > 0)
//             {
//                 currentStep = stepsQueue.Dequeue();
//                 currentStep.Begin();
//             }
//             else
//             {
//                 currentStep = null;
//             }
//         }

//         currentStep?.Update();
//     }
// }