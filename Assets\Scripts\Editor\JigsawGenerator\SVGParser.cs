using UnityEngine;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace JigsawGenerator
{
    public static class SVGParser
    {
        // 贝塞尔曲线采样点数量
        private const int BEZIER_SEGMENTS = 12;
        // 解析SVG路径数据
        public static List<List<Vector2>> ParseSVGPath(string pathData)
        {
            List<List<Vector2>> paths = new List<List<Vector2>>();
            List<Vector2> currentPath = null;

            // 匹配SVG路径命令和坐标
            string pattern = @"([MLHVCSQTAZmlhvcsqtaz])|([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)";
            MatchCollection matches = Regex.Matches(pathData, pattern);

            char command = ' ';
            List<float> parameters = new List<float>();
            Vector2 currentPoint = Vector2.zero;
            Vector2 firstPoint = Vector2.zero;

            foreach (Match match in matches)
            {
                if (match.Groups[1].Success) // 命令
                {
                    if (parameters.Count > 0)
                    {
                        ExecutePathCommand(ref currentPath, ref paths, command, parameters, ref currentPoint, ref firstPoint);
                        parameters.Clear();
                    }

                    command = match.Groups[1].Value[0];

                    // 如果是移动命令，创建新路径
                    if (command == 'M' || command == 'm')
                    {
                        currentPath = new List<Vector2>();
                        paths.Add(currentPath);
                    }
                }
                else if (match.Groups[2].Success) // 坐标参数
                {
                    parameters.Add(float.Parse(match.Groups[2].Value));
                }
            }

            // 处理最后一组参数
            if (parameters.Count > 0)
            {
                ExecutePathCommand(ref currentPath, ref paths, command, parameters, ref currentPoint, ref firstPoint);
            }

            return paths;
        }

        // 执行SVG路径命令
        private static void ExecutePathCommand(ref List<Vector2> currentPath, ref List<List<Vector2>> paths,
                                              char command, List<float> parameters, ref Vector2 currentPoint, ref Vector2 firstPoint)
        {
            switch (command)
            {
                case 'M': // 绝对移动
                    for (int i = 0; i < parameters.Count; i += 2)
                    {
                        if (i + 1 < parameters.Count)
                        {
                            currentPoint = new Vector2(parameters[i], parameters[i + 1]);
                            if (i == 0)
                            {
                                firstPoint = currentPoint;
                            }
                            currentPath.Add(currentPoint);
                        }
                    }
                    break;

                case 'm': // 相对移动
                    for (int i = 0; i < parameters.Count; i += 2)
                    {
                        if (i + 1 < parameters.Count)
                        {
                            currentPoint += new Vector2(parameters[i], parameters[i + 1]);
                            if (i == 0)
                            {
                                firstPoint = currentPoint;
                            }
                            currentPath.Add(currentPoint);
                        }
                    }
                    break;

                case 'L': // 绝对线段
                    for (int i = 0; i < parameters.Count; i += 2)
                    {
                        if (i + 1 < parameters.Count)
                        {
                            currentPoint = new Vector2(parameters[i], parameters[i + 1]);
                            currentPath.Add(currentPoint);
                        }
                    }
                    break;

                case 'l': // 相对线段
                    for (int i = 0; i < parameters.Count; i += 2)
                    {
                        if (i + 1 < parameters.Count)
                        {
                            currentPoint += new Vector2(parameters[i], parameters[i + 1]);
                            currentPath.Add(currentPoint);
                        }
                    }
                    break;

                case 'H': // 绝对水平线
                    for (int i = 0; i < parameters.Count; i++)
                    {
                        currentPoint = new Vector2(parameters[i], currentPoint.y);
                        currentPath.Add(currentPoint);
                    }
                    break;

                case 'h': // 相对水平线
                    for (int i = 0; i < parameters.Count; i++)
                    {
                        currentPoint += new Vector2(parameters[i], 0);
                        currentPath.Add(currentPoint);
                    }
                    break;

                case 'V': // 绝对垂直线
                    for (int i = 0; i < parameters.Count; i++)
                    {
                        currentPoint = new Vector2(currentPoint.x, parameters[i]);
                        currentPath.Add(currentPoint);
                    }
                    break;

                case 'v': // 相对垂直线
                    for (int i = 0; i < parameters.Count; i++)
                    {
                        currentPoint += new Vector2(0, parameters[i]);
                        currentPath.Add(currentPoint);
                    }
                    break;

                case 'Z': // 闭合路径
                case 'z':
                    currentPoint = firstPoint;
                    currentPath.Add(currentPoint);
                    break;

                case 'C': // 三次贝塞尔曲线 (绝对坐标)
                    for (int i = 0; i < parameters.Count; i += 6)
                    {
                        if (i + 5 < parameters.Count)
                        {
                            Vector2 p1 = currentPoint;
                            Vector2 p2 = new Vector2(parameters[i], parameters[i + 1]);
                            Vector2 p3 = new Vector2(parameters[i + 2], parameters[i + 3]);
                            Vector2 p4 = new Vector2(parameters[i + 4], parameters[i + 5]);

                            // 添加贝塞尔曲线的采样点
                            AddCubicBezierPoints(currentPath, p1, p2, p3, p4);

                            currentPoint = p4;
                        }
                    }
                    break;

                case 'c': // 三次贝塞尔曲线 (相对坐标)
                    for (int i = 0; i < parameters.Count; i += 6)
                    {
                        if (i + 5 < parameters.Count)
                        {
                            Vector2 p1 = currentPoint;
                            Vector2 p2 = currentPoint + new Vector2(parameters[i], parameters[i + 1]);
                            Vector2 p3 = currentPoint + new Vector2(parameters[i + 2], parameters[i + 3]);
                            Vector2 p4 = currentPoint + new Vector2(parameters[i + 4], parameters[i + 5]);

                            // 添加贝塞尔曲线的采样点
                            AddCubicBezierPoints(currentPath, p1, p2, p3, p4);

                            currentPoint = p4;
                        }
                    }
                    break;

                case 'S': // 平滑三次贝塞尔曲线 (绝对坐标)
                case 's': // 平滑三次贝塞尔曲线 (相对坐标)
                    Debug.LogWarning("SVG平滑三次贝塞尔曲线命令暂不支持: " + command);
                    break;

                case 'Q': // 二次贝塞尔曲线 (绝对坐标)
                case 'q': // 二次贝塞尔曲线 (相对坐标)
                case 'T': // 平滑二次贝塞尔曲线 (绝对坐标)
                case 't': // 平滑二次贝塞尔曲线 (相对坐标)
                    Debug.LogWarning("SVG二次贝塞尔曲线命令暂不支持: " + command);
                    break;

                case 'A': // 圆弧 (绝对坐标)
                case 'a': // 圆弧 (相对坐标)
                    // 简化处理：只添加终点，不计算圆弧
                    if (parameters.Count >= 7)
                    {
                        if (command == 'A')
                        {
                            currentPoint = new Vector2(parameters[parameters.Count - 2], parameters[parameters.Count - 1]);
                        }
                        else // 'a'
                        {
                            currentPoint += new Vector2(parameters[parameters.Count - 2], parameters[parameters.Count - 1]);
                        }
                        currentPath.Add(currentPoint);
                    }
                    break;
            }
        }
        // 添加三次贝塞尔曲线的采样点
        private static void AddCubicBezierPoints(List<Vector2> path, Vector2 p1, Vector2 p2, Vector2 p3, Vector2 p4)
        {
            // 添加起点
            if (path.Count == 0 || path[path.Count - 1] != p1)
            {
                path.Add(p1);
            }

            // 计算贝塞尔曲线上的点
            for (int i = 1; i <= BEZIER_SEGMENTS; i++)
            {
                float t = i / (float)BEZIER_SEGMENTS;
                Vector2 point = CubicBezierPoint(p1, p2, p3, p4, t);
                path.Add(point);
            }
        }

        // 计算三次贝塞尔曲线上的点
        private static Vector2 CubicBezierPoint(Vector2 p1, Vector2 p2, Vector2 p3, Vector2 p4, float t)
        {
            float u = 1 - t;
            float tt = t * t;
            float uu = u * u;
            float uuu = uu * u;
            float ttt = tt * t;

            Vector2 point = uuu * p1;
            point += 3 * uu * t * p2;
            point += 3 * u * tt * p3;
            point += ttt * p4;

            return point;
        }
    }
}
