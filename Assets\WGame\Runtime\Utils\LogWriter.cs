﻿using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using UnityEngine;

public class LogWriter : MonoBehaviour
{
    public bool enable = true;
    [Tooltip("每个日志文件大小限制，单位MB")]
    public int fileSizeLimitMB = 100;
    [Toolt<PERSON>("最多存在几个日志文件")]
    public int maxLogFileCount = 10;
    [Tooltip("日志多少条才写入文件")]
    public int writeCacheCount = 10;
    
    private string logFilePath;
    private List<string> logCache = new List<string>();
    private int logCacheCount;
    private Dictionary<string, int> dateIndexDic = new Dictionary<string, int>();
    private readonly object fileLock = new object();
    private string timeformat = "yyyy.MM.dd";
    private string timeNow;
    private string logDirectoryPath;
    private StringBuilder tempStr = new StringBuilder();
    private long maxfileSizeLimitMB;
    void Awake()
    {
        if (!enable)
            return;

        maxfileSizeLimitMB = fileSizeLimitMB * 1024 * 1024;
        logDirectoryPath = Application.persistentDataPath+ "/Logs/";
        string timestamp = System.DateTime.Now.ToString(timeformat);
        if (PlayerPrefsManager.HasKey(timestamp))
        {
            dateIndexDic.Add(timestamp, PlayerPrefsManager.GetInt(timestamp));
        }
        
        logFilePath = GetLogFilePath();
        timeNow = System.DateTime.Now.ToString();
        Application.logMessageReceived += LogCallback;
    }

    private string GetLogFilePath(bool newfile = false)
    {
        string timestamp = System.DateTime.Now.ToString(timeformat);
        if (!dateIndexDic.TryGetValue(timestamp, out int index))
        {
            index = 0;
            dateIndexDic.Add(timestamp, index);
        }
        else
        {
            if (newfile)
            {
                index++;
                dateIndexDic[timestamp] = index;

                DeleteOldLog();
            }
        }
        var path = logDirectoryPath + $"{timestamp}.{index}.log";
        //Debug.Log("=======:"+ path);
        return path;
    }
    
    public void Log(string msg)
    {
        LogCallback(msg,"",LogType.Log);
    }
    
    private void LogCallback(string condition, string stackTrace, LogType type)
    {
        tempStr.Clear();
        tempStr.Append(timeNow);
        tempStr.Append("\t");
        tempStr.Append(condition);
        logCache.Add(tempStr.ToString());
        if (type == LogType.Exception || type == LogType.Error)
        {
            logCache.Add(stackTrace);
        }
        logCacheCount++;
    }

    
    private void Update()
    {
        if (!enable)
            return;
        
        timeNow = System.DateTime.Now.ToString();
        if (logCacheCount >= writeCacheCount)
        {
            logCacheCount = 0;
            WriteLogsToFileAsync();
        }
    }
    
    private void DeleteOldLog()
    {
        LogFileComparer logFileComparer = new LogFileComparer();
        string[] files = Directory.GetFiles(logDirectoryPath).OrderBy(s => s, logFileComparer).ToArray();
        int filesToDelete = Mathf.Clamp(files.Length - maxLogFileCount, 0, int.MaxValue);
        for (int i = 0; i < filesToDelete; i++) File.Delete(files[i]);
    }
    
    private async void WriteLogsToFileAsync()
    {
        try
        {
            List<string> logsToWrite = new List<string>(logCache);
            logCache.Clear();

            await Task.Run(() =>
            {
                lock (fileLock)
                {
                    string directoryPath = Path.GetDirectoryName(logFilePath);
                    if (!Directory.Exists(directoryPath))
                    {
                        Directory.CreateDirectory(directoryPath);
                    }
                    if (GetFileSize(logFilePath) >= maxfileSizeLimitMB)
                    {
                        logFilePath = GetLogFilePath(true);
                    }
                    using (StreamWriter writer = new StreamWriter(logFilePath, true))
                    {
                        
                        foreach (string log in logsToWrite)
                        {
                            writer.WriteLine(log);
                        }
                    }
                }
            });

            string timestamp = System.DateTime.Now.ToString(timeformat);
            if (dateIndexDic.TryGetValue(timestamp, out int index))
            {
                PlayerPrefs.SetInt(timestamp, index);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning("Failed to write logs to file."+e.Message);
        }
    }
    
    private long GetFileSize(string filePath)
    {
        if (File.Exists(filePath))
        {
            FileInfo fileInfo = new FileInfo(filePath);
            return fileInfo.Length;
        }
        return 0;
    }

    private void OnDestroy()
    {
        Application.logMessageReceived -= LogCallback;
        if (logCache.Count > 0)
        {
            WriteLogsToFileAsync();
        }
    }

    public class LogFileComparer : IComparer<string>
    {
        public int Compare(string x, string y)
        {
            IList<int> xNumbers = ExtractNumbersFromFileName(x);
            IList<int> yNumbers = ExtractNumbersFromFileName(y);

            for (int i = 0; i < Mathf.Min(xNumbers.Count, yNumbers.Count); i++)
            {
                int comparisonResult = xNumbers[i].CompareTo(yNumbers[i]);
                if (comparisonResult != 0)
                {
                    return comparisonResult;
                }
            }

            return xNumbers.Count.CompareTo(yNumbers.Count);
        }

        private IList<int> ExtractNumbersFromFileName(string fileName)
        {
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
            var matches = Regex.Matches(fileNameWithoutExtension, @"\d+");

            return matches.Select(m => int.Parse(m.Value)).ToList();
        }
    }
}
