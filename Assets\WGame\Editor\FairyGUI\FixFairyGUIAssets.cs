﻿using System.Diagnostics;
using System.IO;
using UnityEditor;
using UnityEngine;
public class FixFairyGUIAssets : MonoBehaviour
{
    [MenuItem("Tools/FixFairyGUIAssets", priority = 9)]
    public static void SetSpineMatToAlpha()
    {
        SetSpineMatToAlpha("Assets/_MyGame");
        UnGenerateMipMaps("Assets/_MyGame/Bundles/UI/FairyGUI");
        UnityEngine.Debug.Log("======Done!=====");
    }

    public static void UnGenerateMipMaps(string fullPath)
    {
        if (Directory.Exists(fullPath))
        {
            string rootPath = Application.dataPath.Replace("Assets", "");
            DirectoryInfo direction = new DirectoryInfo(fullPath);
            FileInfo[] files = direction.GetFiles("*.png", SearchOption.AllDirectories);
            for (int i = 0; i < files.Length; i++)
            {
                string path = files[i].FullName.Replace("\\", "/").Replace(rootPath, "");
                TextureImporter texture = AssetImporter.GetAtPath(path) as TextureImporter;
                if (texture != null)
                {
                    if (texture.mipmapEnabled)
                    {
                        texture.mipmapEnabled = false;
                        texture.SaveAndReimport();
                        //Debug.Log("success: " + path);
                    }
                }
            }

        }
    }

    public static void SetSpineMatToAlpha(string fullPath)
    {
        if (Directory.Exists(fullPath))
        {
            string rootPath = Application.dataPath.Replace("Assets", "");
            DirectoryInfo direction = new DirectoryInfo(fullPath);
            FileInfo[] files = direction.GetFiles("*.mat", SearchOption.AllDirectories);
            for (int i = 0; i < files.Length; i++)
            {
                string path = files[i].FullName.Replace("\\","/").Replace(rootPath, "");
                Material mat = AssetDatabase.LoadAssetAtPath<Material>(path);
                if(mat.shader.name == "Spine/Skeleton")
                {
                    mat.SetFloat("_StraightAlphaInput", 1);
                    mat.EnableKeyword("_STRAIGHT_ALPHA_INPUT");

                    string texturePath = System.IO.Path.GetDirectoryName(path);
                    texturePath = System.IO.Path.Combine(texturePath, mat.mainTexture.name+".png");
                    TextureImporter texture = AssetImporter.GetAtPath(texturePath) as TextureImporter;
                    if(texture != null)
                    {
                        if (!texture.alphaIsTransparency)
                        {
                            texture.alphaIsTransparency = true;
                            texture.SaveAndReimport();
                        }
                    }
                    //Debug.Log("success: " + files[i].Name);
                }
            }

        }
    }
}