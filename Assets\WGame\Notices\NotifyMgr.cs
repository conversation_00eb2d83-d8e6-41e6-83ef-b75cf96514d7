﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
public class NotifyMgr
{
    public delegate void SimpleCallBack();
    public delegate void TypeCallBack<T>(T t);

    public static void On(string type, object caller, SimpleCallBack listener)
    {
        _instance._on(type, caller, listener);
    }
    public static void On(string type, object caller, TypeCallBack<object> listener)
    {
        _instance._on(type, caller, listener);
    }
    public static void Off(string type, object caller, SimpleCallBack listener)
    {
        _instance._off(type, caller, listener);
    }
    public static void Off(string type, object caller, TypeCallBack<object> listener)
    {
        _instance._off(type, caller, listener);
    }
    public static void OffAllCaller(object caller)
    {
        _instance._offAllCaller(caller);
    }
    public static void Event (string type, object data = null)
    {
        _instance._event (type, data);
    }

    private static NotifyMgr _instance = new NotifyMgr();
    private static readonly Dictionary<string, List<Handler>> eventDic = new Dictionary<string, List<Handler>>();
    private void _on(string type, object caller, SimpleCallBack listener)
    {
        _off(type, caller, listener);
        var eventList = _getEventList(type);
        var hander = Handler.Create(type, caller, listener);
        eventList.Add(hander);
    }
    private void _on(string type, object caller, TypeCallBack<object> listener)
    {
        _off(type, caller, listener);
        var eventList = _getEventList(type);
        var hander = Handler.Create(type, caller, listener);
        eventList.Add(hander);
    }
    private List<Handler> _getEventList(string type)
    {
        eventDic.TryGetValue(type, out List<Handler> eventList);
        if (eventList == null)
        {
            eventList = new List<Handler>();
            eventDic.Add(type, eventList);
        }
        return eventList;
    }

    private void _off(string type, object caller, object listener)
    {
        eventDic.TryGetValue(type, out List<Handler> eventList);
        if (eventList != null)
        {
            for (int i = 0; i < eventList.Count; i++)
            {
                var handler = eventList[i];
                if (handler.caller.Equals(caller) && 
                    (listener == null || 
                    (handler.method0 !=null && handler.method0.Equals(listener)) || 
                    (handler.method1 != null && handler.method1.Equals(listener))))
                {
                    Handler.Recover(eventList[i]);
                    eventList.RemoveAt(i);
                    i--;
                }
            }
        }
    }

    private void _offAllCaller(object caller)
    {
        foreach (var item in eventDic)
        {
            _off(item.Key, caller, null);
        }
    }

    private void _event(string type, object data = null)
    {
        eventDic.TryGetValue(type, out List<Handler> eventList);
        if (eventList != null)
        {
            for(var i = 0; i < eventList.Count; i++)
            {
                eventList[i].RunWith(data);
            }
        }
    }
}

class Handler
{
    public string type;
    public NotifyMgr.SimpleCallBack method0;
    public NotifyMgr.TypeCallBack<object> method1;
    public object caller;
    public void RunWith(object data)
    {
        method0?.Invoke();
        method1?.Invoke(data);
    }

    private static Stack<Handler> handlerList = new Stack<Handler>();
    public static Handler Create(string type,object caller, NotifyMgr.SimpleCallBack listener)
    {
        var handler = _Create(type, caller);
        handler.method0 = listener;
        return handler;
    }
    public static Handler Create(string type, object caller, NotifyMgr.TypeCallBack<object> listener)
    {
        var handler = _Create(type, caller);
        handler.method1 = listener;
        return handler;
    }
    private static Handler _Create(string type, object caller)
    {
        var handler = handlerList.Count > 0 ? handlerList.Pop() : new Handler();
        handler.type = type;
        handler.caller = caller;
        return handler;
    }
    public static void Recover(Handler handler)
    {
        handler.type = null;
        handler.caller = null;
        handler.method0 = null;
        handler.method1 = null;
        handlerList.Push(handler);
    }
}

