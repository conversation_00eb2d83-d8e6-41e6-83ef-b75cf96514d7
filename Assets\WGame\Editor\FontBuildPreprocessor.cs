using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;
using UnityEngine;

public class FontBuildPreprocessor : IPreprocessBuildWithReport, IPostprocessBuildWithReport
{
    private const string SOURCE_FOLDER = "Assets/_MyGame/RawRes/Fonts";
    private const string OUTPUT_FOLDER = "Assets/_MyGame/Resources/Bundles/Fonts";
    private static List<string> _movedFiles = new List<string>();

    public int callbackOrder => 0;

    // 构建前处理
    public void OnPreprocessBuild(BuildReport report)
    {
#if !YOOASSET
        ProcessFont(OUTPUT_FOLDER);
#endif
    }

    // 构建后处理
    public void OnPostprocessBuild(BuildReport report)
    {
        // 将TTF文件移回原位置
#if !YOOASSET
        RestoreTTFFiles(OUTPUT_FOLDER);
        Debug.Log("构建后处理完成");
#endif
    }


    // 检查是否存在tmp字体资源
    private static bool HasTMPFont(string outputFolder)
    {
        // 检查输出目录是否存在
        if (Directory.Exists(outputFolder))
        {
            string[] fontAssets = Directory.GetFiles(outputFolder, "*.asset", SearchOption.TopDirectoryOnly);
            if (fontAssets.Length > 0)
            {
                return true;
            }
        }
        return false;
    }

    // 检查是否存在ttf字体资源
    private static bool HasTTFFont(string outputFolder)
    {
        // 检查输出目录是否存在
        if (Directory.Exists(outputFolder))
        {
            string[] fontAssets = Directory.GetFiles(outputFolder, "*.ttf", SearchOption.TopDirectoryOnly);
            if (fontAssets.Length > 0)
            {
                return true;
            }
        }
        return false;
    }

    public static void ProcessFont(string outputFolder)
    {
        Debug.Log("开始构建前处理：收集字符和创建字体资源");

        // 执行字符收集
        CollectCharsTools.CollectChars();
        TTFMini.CreateFontAssets(() =>
        {
            CreateTMPAsset.CreateFontAssets();
        });

        // TTFMini.CreateFontAssets();
        Debug.Log("构建前处理完成");
    }

    // 移动TTF文件到临时位置
    private static void MoveTTFFilesToTemp()
    {
        _movedFiles.Clear();

        string tempFolder = Path.Combine(Application.dataPath, "../Temp/FontsBackup");
        if (!Directory.Exists(tempFolder))
        {
            Directory.CreateDirectory(tempFolder);
        }

        string[] ttfFiles = Directory.GetFiles(SOURCE_FOLDER, "*.ttf", SearchOption.AllDirectories);
        foreach (string file in ttfFiles)
        {
            string fileName = Path.GetFileName(file);
            string destPath = Path.Combine(tempFolder, fileName);

            File.Copy(file, destPath, true);
            File.Delete(file);

            _movedFiles.Add(file);
            Debug.Log($"临时移动字体文件: {file} -> {destPath}");
        }

        AssetDatabase.Refresh();
    }

    // 恢复TTF文件
    public static void RestoreTTFFiles(string outputFolder)
    {
        if (!HasTMPFont(outputFolder))
            return;

        string tempFolder = Path.Combine(Application.dataPath, "../Temp/FontsBackup");

        foreach (string originalPath in _movedFiles)
        {
            string fileName = Path.GetFileName(originalPath);
            string sourcePath = Path.Combine(tempFolder, fileName);

            if (File.Exists(sourcePath))
            {
                // 确保目标目录存在
                Directory.CreateDirectory(Path.GetDirectoryName(originalPath));

                File.Copy(sourcePath, originalPath, true);
                Debug.Log($"恢复字体文件: {sourcePath} -> {originalPath}");
            }
        }

        AssetDatabase.Refresh();
    }
}