
#if WXGAME
using System.IO;
using UnityEditor;
using UnityEngine;
using WeChatWASM;

class BuildWxgame
{
    [UnityEditor.Callbacks.PostProcessBuild(10)]
    private static void OnPostprocessBuild(BuildTarget target, string pathToBuiltProject)
    {
        CopyOpenData();
    }

    [MenuItem("Build/BuildWxgame", priority = 0)]
    private static void BuildBundles()
    {
        WXConvertCore.config.ProjectConf.CDN = GameConfig.CdnUrl;
        if (WXConvertCore.config.ProjectConf.assetLoadType != 1)
        {
            WXConvertCore.config.ProjectConf.assetLoadType = 1;//首包资源放到包内
        }
        EditorUtility.SetDirty(WXConvertCore.config);
        AssetDatabase.SaveAssets();
        
        if (WeChatWASM.WXConvertCore.DoExport() == WeChatWASM.WXConvertCore.WXExportError.SUCCEED)
        {
            Debug.Log("===发布成功===");

            CopyOpenData();
            CopySdk();
        }
        else
        {
            Debug.LogError("失败");
        }
    }
    private static void CopyOpenData()
    {
        var srcOpenDataDir = Path.Join(Application.dataPath, "../OpenDataProject/open-data");
        var targetOpenDataDir = Path.Join(WeChatWASM.WXConvertCore.config.ProjectConf.DST, WeChatWASM.WXConvertCore.miniGameDir, "open-data");
        CopyDirectory(srcOpenDataDir, targetOpenDataDir);
        Debug.Log("CopyOpenData Success!");
    }

    private static void CopySdk()
    {
        var srcOpenDataDir = Path.Join(Application.dataPath, "Sdk");
        var targetOpenDataDir = Path.Join(WeChatWASM.WXConvertCore.config.ProjectConf.DST, WeChatWASM.WXConvertCore.miniGameDir, "sdk");

        CopyDirectory(srcOpenDataDir, targetOpenDataDir);
        Debug.Log("CopySdk Success!");
    }

    private static void CopyDirectory(string srcDir, string targetDir, bool clearTargetDir = true)
    {

        if (!Directory.Exists(srcDir)) return;
        if (clearTargetDir && Directory.Exists(targetDir))
        {
            Directory.Delete(targetDir, true);
        }

        if (!Directory.Exists(targetDir))
        {
            Directory.CreateDirectory(targetDir);
        }

        string[] files = Directory.GetFiles(srcDir);
        string[] directories = Directory.GetDirectories(srcDir);
        foreach (string file in files)
        {
            string fileName = Path.GetFileName(file);
            if (fileName.Contains(".meta"))
                continue;
            string destination = Path.Combine(targetDir, fileName);

            File.Copy(file, destination, true);
        }

        foreach (string directory in directories)
        {
            string directoryName = Path.GetFileName(directory);
            if (directoryName == "Plugins")
                continue;
            string destination = Path.Combine(targetDir, directoryName);
            CopyDirectory(directory, destination, false);
        }
    }

}
#endif

