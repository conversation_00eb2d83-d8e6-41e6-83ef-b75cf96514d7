﻿
using System;
using System.Collections.Generic;
using FairyGUI;
using UnityEngine;

public class Panel
{
    protected string packName;
    protected string compName;
    protected string[] extraPacks;//需要使用到的额外包资源
    protected bool isBlurLayer = false;
    protected bool modal = false;
    protected bool inQueue = false;
    protected bool isFullScreen = true;
    private bool cache = false;
    protected GComponent contentPane;
    public Action<int> OnClosed;
    protected List<MediatorBase> _mediatorList;
    protected bool disableAnimation; //屏蔽显示和隐藏动画
    protected bool isClickClose = true;
    protected Window _window;
    private void Init(GComponent comp)
    {
        _window = new Window();
        contentPane = _window.contentPane = comp;
        if (isFullScreen)
        {
            _window.MakeFullScreen();
        }

#if UNITY_STANDALONE
        //窗口改变自适应
        _window.AddRelation(GRoot.inst, RelationType.Size);
#endif
        // NetConnection.GetInstance().OnMessageReceived += OnMessageReceived;
        contentPane.onClick.Add(OnClick);
        _window.modal = modal;

        DoInitialize();
    }

    public static float ButtleCdTime = 0.3f;
    private Dictionary<string, float> buttonCdDic = new Dictionary<string, float>();
    private void OnClick(EventContext context)
    {
        var target = context.initiator as DisplayObject;
        if (target == null || target.gOwner == null)
            return;

        var targetName = target.gOwner.name;
        buttonCdDic.TryGetValue(targetName, out float preClickTime);
        if (Time.realtimeSinceStartup - preClickTime < ButtleCdTime)
        {
            return;
        }
        buttonCdDic[targetName] = Time.realtimeSinceStartup;
        this.OnMouseClick(targetName);
    }

    protected void matchScreen(GObject img)
    {
        float s1 = GRoot.inst.width / img.width;
        float s2 = GRoot.inst.height / img.height;
        float s = s1 > s2 ? s1 : s2;
        img.scale = Vector2.one * s;
        img.x = -(img.width * s - GRoot.inst.width) / 2f;
        img.y = -(img.height * s - GRoot.inst.height) / 2f;
    }

    protected virtual void DoInitialize()
    {

    }
    // protected virtual void OnMessageReceived(NetMessage msg)
    // {

    // }
    protected virtual void OnMouseClick(string targetName)
    {

    }
    public string GetCurPackRes(string name)
    {
        return "ui://" + this.packName + "/" + name;
    }
    protected bool IsShowing
    {
        get
        {
            return _window.isShowing;
        }
    }

    public void Show()
    {
        DoShowAnimation();
    }

    protected virtual void OnShow()
    {

    }

    private void CheckIsTopWindowAndHide()
    {
        var panel = GRoot.inst.GetTopWindow();
        if (panel.modal)
        {
            if (panel == _window)
            {
                if (isClickClose)
                {
                    Hide();
                }
            }
        }
        else
        {
            var childrens = GRoot.inst._children;
            for (var i = 0; i < childrens.Count; i++)
            {
                var child = childrens[i] as Window;
                if (child != null && child.modal)
                {
                    if (child == _window)
                    {
                        if (isClickClose)
                        {
                            Hide();
                        }
                    }
                    return;
                }
            }
        }

    }

    public void Hide(int closeType = 0)
    {
        // NetConnection.GetInstance().OnMessageReceived -= OnMessageReceived;
        GRoot.inst.modalLayer.onClick.Remove(CheckIsTopWindowAndHide);
        DoHideAnimation(closeType);
    }

    protected MediatorBase AddMediator(MediatorBase mediator)
    {
        if (_mediatorList == null)
        {
            _mediatorList = new List<MediatorBase>();
        }
        _mediatorList.Add(mediator);
        return mediator;
    }

    protected void RemoveMediator(MediatorBase mediator)
    {
        if (_mediatorList != null)
        {
            this._mediatorList.Remove(mediator);
        }
    }

    private void _ClearAllMediator()
    {
        if (_mediatorList != null)
        {
            _mediatorList.ForEach((MediatorBase mediator) =>
            {
                mediator.Remove();
            });
            _mediatorList = null;
        }
    }

    protected virtual void OnHide()
    {

    }


    //处理显示动画
    protected void DoShowAnimation()
    {
        if (disableAnimation)
        {
            _window.Show();
            ShowComplete();
            return;
        }

        _window.Show();
        var EnterTween = contentPane.GetTransition("EnterTween");
        if (EnterTween != null)
        {
            EnterTween.Play(() =>
            {
                ShowComplete();
            });
        }
        else if (modal)
        {
            contentPane.SetScale(0f, 0f);
            contentPane.TweenScale(new Vector2(1.0f, 1.0f), 0.3f).SetEase(EaseType.BackOut).SetIgnoreEngineTimeScale(true);
            contentPane.alpha = 0.5f;
            contentPane.SetPivot(0.5f, 0.5f);
            contentPane.TweenFade(1f, 0.3f).SetIgnoreEngineTimeScale(true).OnComplete(ShowComplete);
        }
        else
        {
            ShowComplete();
        }
    }
    private void ShowComplete()
    {
        if (modal && isClickClose)
        {
            contentPane.opaque = false;
            GRoot.inst.modalLayer.onClick.Remove(CheckIsTopWindowAndHide);
            GRoot.inst.modalLayer.onClick.Add(CheckIsTopWindowAndHide);
        }
        OnShow();
    }

    //处理隐藏动画
    protected virtual void DoHideAnimation(int closeType = 0)
    {
        if (disableAnimation)
        {
            HidePanel(closeType);
            return;
        }
        HidePanel(closeType);
    }

    protected void HidePanel(int closeType = 0)
    {
        NotifyMgr.OffAllCaller(this);
        OnClosed?.Invoke(closeType);
        OnHide();
        _window.Hide();

        if (!cache)
        {
            this._ClearAllMediator();
            _window.Dispose();
        }

        if (inQueue)
        {
            RemoveLastPanelFromQueue();
            TryShowNextPanel();
        }
    }


    private void _Create<T>(Action<T> callBack = null, bool isAsync = false) where T : Panel, new()
    {
        if (inQueue)
        {
            AddQueue(panel => callBack?.Invoke((T)panel), isAsync);
        }
        else
        {
            ShowPanel(panel => callBack?.Invoke((T)panel), isAsync);
        }
    }

    private void ShowPanel(Action<Panel> callBack, bool isAsync = false)
    {
        if (_window == null)
        {
            if (modal)
            {
                SmallLoadingPanel.Show();
            }
            FUILoader.LoadPackage(packName, extraPacks, () =>
            {
                if (isAsync)
                {
                    UIPackage.CreateObjectAsync(packName, compName, (res) =>
                    {
                        if (modal)
                        {
                            SmallLoadingPanel.Hide();
                        }
                        this.Init(res.asCom);
                        this.Show();

                        callBack?.Invoke(this);
                    });
                }
                else
                {
                    if (modal)
                    {
                        SmallLoadingPanel.Hide();
                    }
                    this.Init(UIPackage.CreateObject(packName, compName).asCom);
                    this.Show();
                    callBack?.Invoke(this);
                }
            });
        }
        else
        {
            Show();
            callBack?.Invoke(this);
        }

    }

    private static SimplePool<PanelInfo> panelInfoPool = new SimplePool<PanelInfo>(CreatePanelInfo);
    private static PanelInfo CreatePanelInfo()
    {
        return new PanelInfo();
    }

    private static Queue<PanelInfo> queuePanel = new Queue<PanelInfo>();
    private static PanelInfo curQueuePanel;
    private void AddQueue(Action<Panel> callBack, bool isAsync = false)
    {
        var info = panelInfoPool.Get();
        info.panel = this;
        info.isAsync = isAsync;
        info.callBack = callBack;
        queuePanel.Enqueue(info);

        TryShowNextPanel();
    }
    private void RemoveLastPanelFromQueue()
    {
        if (curQueuePanel != null)
        {
            panelInfoPool.Release(curQueuePanel);
            curQueuePanel = null;
        }
    }

    private void TryShowNextPanel()
    {
        if (curQueuePanel == null && queuePanel.Count > 0)
        {
            curQueuePanel = queuePanel.Dequeue();
            curQueuePanel.panel.ShowPanel(curQueuePanel.callBack, curQueuePanel.isAsync);
        }
    }
    //todo 缓存清理时机
    private static Dictionary<string, Panel> cacheDic = new Dictionary<string, Panel>();

    /// <summary>
    /// 创建界面
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="callBack"></param>
    /// <param name="cache">如果需要缓存界面，逻辑上需要支持重复使用
    /// DoInitialize只有创建的时候调用一次，需要重复调用的逻辑需要覆写OnShow</param>
    public static void Create<T>(Action<T> callBack = null, bool cache = false, bool isAsync = false) where T : Panel, new()
    {
        Type panelType = typeof(T);
        Panel panel = null;
        if (cache)
        {
            cacheDic.TryGetValue(panelType.ToString(), out panel);
        }

        if (panel == null)
        {
            panel = new T();

            if (cache)
            {
                cacheDic.Add(panelType.ToString(), panel);
            }
        }

        panel.cache = cache;
        panel._Create<T>(callBack, isAsync);
    }


    public class PanelInfo
    {
        public Panel panel;
        public Action<Panel> callBack;
        internal bool isAsync;
        public bool cache;
        public Queue<PanelInfo> queue;
    }
}

