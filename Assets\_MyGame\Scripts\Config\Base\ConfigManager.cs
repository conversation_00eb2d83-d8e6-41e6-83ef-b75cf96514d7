﻿using System;
using System.Collections.Generic;
using UnityEngine;

public class ConfigManager
{
    private ConfigManager() { }

    private static Dictionary<string, ConfigBase> configDic = new Dictionary<string, ConfigBase>();
    public static void CreateByJsonStr<T, TInfo>(string jsonStr) where T : ConfigBase, new() where TInfo : ConfigInfoBase, new()
    {
        CreateByJsonStr(typeof(T), typeof(TInfo), jsonStr);
    }

    internal static void CreateByJsonStr(Type configType, Type infoType, string text)
    {
        var key = configType.ToString();
        var config = Activator.CreateInstance(configType) as ConfigBase;
        config.Parse(infoType, text);
        configDic[key] = config;
    }

    internal static void CreateByConfigInfo(Type configType, ConfigInfoBase[] infos)
    {
        var key = configType.ToString();
        var config = Activator.CreateInstance(configType) as ConfigBase;
        if (infos == null)
        {
            Debug.Assert(false, $"{configType} infos is null");
            return;
        }

        for (int i = 0; i < infos.Length; i++)
        {
            var info = infos[i];
            config.CacheData(info.uniqueKey, info);
            configDic[key] = config;
        }
    }

    public static ConfigBase GetConfig<T>() where T : ConfigBase,new()
    {
        var key = typeof(T).ToString();
        configDic.TryGetValue(key,out ConfigBase config);
        if (config == null)
            return new T();
        return config;
    }

    public static void ResetAll()
    {
        foreach (var item in configDic)
        {
            item.Value.Reset();
        }
        configDic.Clear();
    }

}