﻿using System.IO;
using UnityEngine;

public class ImageCache
{
    public static string GetImagePathWithURL(string url)
    {
        if (url.IndexOf("http") < 0)
        {
            return null;
        }

        string fileName = MD5Util.Encrypt(url);
        string path = System.IO.Path.Combine(Application.persistentDataPath, "image", fileName);
        if (System.IO.File.Exists(path))
        {
            return path;
        }
        return null;
    }

    public static Texture2D ReadImageWithPath(string imagePath)
    {
        FileStream fs = new FileStream(imagePath, FileMode.Open, FileAccess.Read);
        byte[] buffur = new byte[fs.Length];

        fs.Read(buffur, 0, buffur.Length);
        fs.Close();

        Texture2D texture = new Texture2D(10, 10);
        texture.LoadImage(buffur);
        return texture;
    }
    
    public static void SaveImg(string url, byte[] bytes)
    {
        if (bytes.Length > 0)
        {
            string fileName = MD5Util.Encrypt(url);
            string path = System.IO.Path.Combine(Application.persistentDataPath, "image", fileName);
            SaveFile(bytes, path);
        }
    }

    public static void Clear()
    {
        string path = System.IO.Path.Combine(Application.persistentDataPath, "image");
        if(System.IO.Directory.Exists(path))
        {
            System.IO.Directory.Delete(path);
        }
    }

    public static void CreateDirectoryWhenNotExists(string destination)
    {
        destination = destination.Replace("\\", "/");
        string dir = destination.Substring(0, destination.LastIndexOf("/"));
        if (!Directory.Exists(dir))
        {
            Directory.CreateDirectory(dir);
        }
    }
    
    public static void SaveFile(byte[] bytes, string path)
    {
        CreateDirectoryWhenNotExists(path);

        FileStream output = new FileStream(path, FileMode.Create);
        output.Write(bytes, 0, bytes.Length);

        output.Flush();
        output.Close();
        output.Dispose();
    }
}
