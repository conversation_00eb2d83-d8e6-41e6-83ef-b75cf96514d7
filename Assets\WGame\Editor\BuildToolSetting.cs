﻿using UnityEngine;

[CreateAssetMenu(menuName = "BuildToolSetting")]
public class BuildToolSetting : ScriptableObject
{
    public string DirAOT;
    public string DirDll;
    public string DirFairyGUI;

    private static BuildToolSetting _setting = null;
    public static BuildToolSetting Setting
    {
        get
        {
            if (_setting == null)
                LoadSettingData();
            return _setting;
        }
    }
    private static void LoadSettingData()
    {
        _setting = SettingLoader.LoadSettingData<BuildToolSetting>();
    }
}