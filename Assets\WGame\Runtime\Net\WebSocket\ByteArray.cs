﻿using System;
using System.IO;
using System.Text;

public class ByteArray : MemoryStream
{
    public enum Endian
    {
        LITTER_ENDIAN,
        BIG_ENDIAN
    }

    
    public ByteArray(Endian endian = Endian.BIG_ENDIAN)
    {
        SetRevereFlag(endian);
    }
    public ByteArray(byte[] buffer, Endian endian = Endian.BIG_ENDIAN) : base(buffer)
    {
        SetRevereFlag(endian);
    }

    #region Endian
    private bool needRevereBytes;
    private void SetRevereFlag(Endian endian)
    {
        this.needRevereBytes = (BitConverter.IsLittleEndian && endian != Endian.LITTER_ENDIAN) ||
        (!BitConverter.IsLittleEndian && endian != Endian.BIG_ENDIAN);
    }
    
    private byte[] ReadWithEndian(int count)
    {
        byte[] bytes = new byte[count];
        if (needRevereBytes)
        {
            for (int i = 0; i < count; i++)
            {
                bytes[count - i - 1] = (byte)this.ReadByte();
            }
        }
        else
        {
            for (int i = 0; i < count; i++)
            {
                bytes[i] = (byte)this.ReadByte();
            }
        }
        return bytes;
    }
    
    private void WriteWithEndian(byte[] bytes, int offset, int count)
    {
        if (needRevereBytes)
        {
            for (int i = count -1; i >= 0; i--)
            {
                this.WriteByte(bytes[offset + i]);
            }
        }
        else
        {
            for (int i = 0; i < count; i++)
            {
                this.WriteByte(bytes[offset + i]);
            }
        }
    }
    #endregion

    #region Short
    public short ReadShort()
    {
        byte[] arr = ReadWithEndian(2);
        return BitConverter.ToInt16(arr, 0);
    }
    public void WriteShort(short value)
    {
        byte[] arr = BitConverter.GetBytes(value);
        WriteWithEndian(arr, 0, arr.Length);
    }
    #endregion
    
    #region UShort
    public ushort ReadUShort()
    {
        byte[] arr = ReadWithEndian(2);
        return BitConverter.ToUInt16(arr, 0);
    }
    public void WriteUShort(ushort value)
    {
        byte[] arr = BitConverter.GetBytes(value);
        WriteWithEndian(arr, 0, arr.Length);
    }
    #endregion
    
    #region Int
    public int ReadInt()
    {
        byte[] arr = ReadWithEndian(4);
        return BitConverter.ToInt32(arr, 0);
    }
    public void WriteInt(int value)
    {
        byte[] arr = BitConverter.GetBytes(value);
        WriteWithEndian(arr, 0, arr.Length);
    }
    #endregion
    
    #region UInt
    public uint ReadUInt()
    {
        byte[] arr = ReadWithEndian(4);
        return BitConverter.ToUInt32(arr, 0);
    }
    public void WriteUInt(uint value)
    {
        byte[] arr = BitConverter.GetBytes(value);
        WriteWithEndian(arr, 0, arr.Length);
    }
    #endregion

    #region ULong
    public ulong ReadULong()
    {
        byte[] arr = ReadWithEndian(8);
        return BitConverter.ToUInt64(arr, 0);
    }
    public void WriteULong(ulong value)
    {
        byte[] arr = BitConverter.GetBytes(value);
        WriteWithEndian(arr, 0, arr.Length);
    }
    #endregion

    #region Float
    public float ReadFloat()
    {
        byte[] arr = ReadWithEndian(4);
        return BitConverter.ToSingle(arr, 0);
    }
    public void WriteFloat(float value)
    {
        byte[] arr = BitConverter.GetBytes(value);
        WriteWithEndian(arr, 0, arr.Length);
    }
    #endregion
    
    #region Double
    public double ReadDouble()
    {
        byte[] arr = ReadWithEndian(8);
        return BitConverter.ToDouble(arr, 0);
    }
    public void WriteDouble(double value)
    {
        byte[] arr = BitConverter.GetBytes(value);
        WriteWithEndian(arr, 0, arr.Length);
    }
    #endregion
    
    #region Bool
    public bool ReadBool()
    {
        return base.ReadByte() == 1;
    }
    public void WriteBool(bool value)
    {
        base.WriteByte((byte)(value == true ? 1 : 0));
    }
    #endregion

    #region String
    public string ReadString()
    {
        ushort len = this.ReadUShort();
        byte[] arr = new byte[len];
        base.Read(arr, 0, len);
        return Encoding.UTF8.GetString(arr, 0, len);
    }
    public void WriteString(string value)
    {
        byte[] arr = Encoding.UTF8.GetBytes(value);
        if (arr.Length > 65535)
        {
            throw new Exception("[ByteArray] length out of range");
        }
        this.WriteUShort((ushort)value.Length);
        base.Write(arr, 0, arr.Length);
    }
    #endregion
}

