using System;
using System.Collections.Generic;
using LitJson;
using UnityEngine;

[Serializable]
public class TableSO : ScriptableObject
{
    // public InfoGate[] gates;
    public ConfigInfoBase[] GetDatas(string tableName)
    {
        return tableName switch
        {
            // "Gate" => gates,
            // "GateB" => gatesB,
            // "Item" => items,
            // "Setting" => settings,
            // "Langui" => languis,
            // "Title" => titles,
            // "Theme" => themes,
            // "Decorate" => decorates,
            // "NewFunction" => newFunctions,
            // "Activity" => activities,
            // _ => throw new NotImplementedException(),
        };
    }

    //以下逻辑只在编辑器中有效
#if UNITY_EDITOR
    public static string[] tableNames = new string[] {
        "Gate",
    };

    public void GenerateSO(string tableName, JsonData json)
    {
        switch (tableName)
        {
            // case "Gate":
            //     gates = CreateArray<InfoGate>(json);
            //     break;
            
        }
    }

    public static T[] CreateArray<T>(JsonData json) where T : ConfigInfoBase, new()
    {
        var list = new List<T>();
        if (json.IsArray)
        {
            for (int i = 0; i < json.Count; i++)
            {
                T data = new T();
                data.Parse(json[i]);
                data.uniqueKey = data.GetKey(i).ToString();
                list.Add(data);
            }
        }
        else
        {
            string[] keys = new string[json.Count];
            json.Keys.CopyTo(keys, 0);
            for (int i = 0; i < keys.Length; i++)
            {
                string key = keys[i];
                T data = new T();
                data.Parse(json[key]);
                data.uniqueKey = data.GetKey(i).ToString();
                list.Add(data);
            }
        }
        var result = list.ToArray();
        return result;
    }
#endif
}