using System.Linq;
using PoolEditor;
using UnityEditor;

class PoolAssetPostprocessor : AssetPostprocessor
{
    // static void OnPostprocessAllAssets(
    //     string[] importedAssets,
    //     string[] deletedAssets,
    //     string[] movedAssets,
    //     string[] movedFromAssetPaths)
    // {
    //     if (PoolWindow.Setting == null) return;

    //     var allChangedAssets = importedAssets
    //         .Concat(deletedAssets)
    //         .Concat(movedAssets)
    //         .Concat(movedFromAssetPaths)
    //         .Distinct();

    //     var setting = PoolWindow.Setting;
    //     foreach (var asset in allChangedAssets)
    //     {
    //         foreach (var poolSetting in setting.settings)
    //         {
    //             var poolDirectoryPath = AssetDatabase.GetAssetPath(poolSetting.poolItemFolder);
    //             if (!string.IsNullOrEmpty(poolDirectoryPath) && asset.StartsWith(poolDirectoryPath))
    //             {
    //                 PoolWindow.RefreshPoolData();
    //                 return;
    //             }
    //         }
    //     }
    // }
}