using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 网格位置管理器，负责管理每个网格位置的拼块堆叠情况
/// </summary>
public class GridPositionManager
{
    // 记录每个网格位置的拼块堆叠情况
    private Dictionary<Vector2Int, List<JigsawPiece>> gridPieces = 
        new Dictionary<Vector2Int, List<JigsawPiece>>();
    
    private SmartLayerManager layerManager;
    
    public GridPositionManager(SmartLayerManager manager)
    {
        layerManager = manager;
    }
    
    /// <summary>
    /// 添加拼块到网格位置
    /// </summary>
    /// <param name="gridPos">网格坐标</param>
    /// <param name="piece">拼块</param>
    public void AddPieceToGrid(Vector2Int gridPos, JigsawPiece piece)
    {
        if (!gridPieces.ContainsKey(gridPos))
            gridPieces[gridPos] = new List<JigsawPiece>();
            
        if (!gridPieces[gridPos].Contains(piece))
        {
            gridPieces[gridPos].Add(piece);
        }
    }
    
    /// <summary>
    /// 从网格位置移除拼块
    /// </summary>
    /// <param name="gridPos">网格坐标</param>
    /// <param name="piece">拼块</param>
    public void RemovePieceFromGrid(Vector2Int gridPos, JigsawPiece piece)
    {
        if (gridPieces.ContainsKey(gridPos))
        {
            gridPieces[gridPos].Remove(piece);
            if (gridPieces[gridPos].Count == 0)
            {
                gridPieces.Remove(gridPos);
            }
        }
    }

    /// <summary>
    /// 检查拼块是否处于叠加位置
    /// </summary>
    /// <param name="gridPos">网格坐标</param>
    /// <param name="piece">拼块</param>
    /// <returns>是否为叠加位置</returns>
    public bool IsOverlayPosition(Vector2Int gridPos, JigsawPiece piece)
    {
        if (!gridPieces.ContainsKey(gridPos))
            return false;
            
        var pieces = gridPieces[gridPos];
        int index = pieces.IndexOf(piece);
        return index > 0; // 第一个不是overlay，后续都是overlay
    }
    
    /// <summary>
    /// 获取拼块在网格中的堆叠索引
    /// </summary>
    /// <param name="gridPos">网格坐标</param>
    /// <param name="piece">拼块</param>
    /// <returns>堆叠索引</returns>
    public int GetStackIndex(Vector2Int gridPos, JigsawPiece piece)
    {
        if (!gridPieces.ContainsKey(gridPos))
            return 0;
            
        var pieces = gridPieces[gridPos];
        int index = pieces.IndexOf(piece);
        return index >= 0 ? index : 0;
    }
    
    /// <summary>
    /// 清空所有网格位置数据
    /// </summary>
    public void Clear()
    {
        gridPieces.Clear();
    }

}