using System;
using System.Collections.Generic;

public class SequenceManager
{
    private readonly Queue<Action<SequenceManager>> _queue = new();
    public SequenceManager()
    {

    }

    /// <summary>
    /// 清空队列
    /// </summary>
    public void Clear()
    {
        _queue.Clear();
    }

    /// <summary>
    /// 添加一个任务到队列中
    /// </summary>
    /// <param name="action">要执行的任务</param>
    public void Enqueue(Action<SequenceManager> action)
    {
        _queue.Enqueue(action);
    }

    /// <summary>
    /// 处理队列中的任务
    /// </summary>
    public void ProcessQueue()
    {
        if (_queue.Count > 0)
        {
            var nextAction = _queue.Dequeue();
            nextAction?.Invoke(this);
        }
    }
}
