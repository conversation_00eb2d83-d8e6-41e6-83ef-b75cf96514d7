﻿using System;
using UnityEngine;

public class SoundManager : MonoBehaviour
{
	public static void PlayBg(string name)
	{
        GetInstatnce().bgSound.Play(name, 0);
    }
    public static void StopBg()
    {
        GetInstatnce().bgSound.Stop();
    }

    public static EffectSoundShoot PlayEffect(string name, float pitch = 1f)
    {
        if (string.IsNullOrEmpty(name))
            return null;
        return GetInstatnce().effectSound.Play(name, pitch: pitch);
    }

    public static void SetBgmVolume(float value)
	{
		GetInstatnce().bgSound.SetVolume(value);
	}
	public static void SetEffectVolume(float value)
	{
		GetInstatnce().effectSound.SetVolume(value);
	}

	public static void StopAllAudio()
	{
		GetInstatnce().bgSound.Stop();
        GetInstatnce().effectSound.StopAll();
	}
	public static void ResumeAllAudio()
	{
		GetInstatnce().bgSound.Play();
	}

    public static void SetBgmMute(bool bMute)
    {
        GetInstatnce().bgSound.Mute = bMute;
    }
    public static void SetEffectMute(bool bMute)
    {
        GetInstatnce().effectSound.Mute = bMute;
    }
    private BackgroundSound _bgSound;
    private BackgroundSound bgSound
    {
        get { return _bgSound; }
    }

    private EffectSound _effectSound;
    private EffectSound effectSound
    {
        get { return _effectSound; }
    }

    private void Awake()
    {
        DontDestroyOnLoad(this.gameObject);
        _bgSound = gameObject.AddComponent<BackgroundSound>();
        _effectSound = gameObject.AddComponent<EffectSound>();
    }

    private static SoundManager instance;
    public static SoundManager GetInstatnce()
    {
        if (instance == null)
        {
            var go = new GameObject();
            go.name = "SoundManager";
            instance = go.AddComponent<SoundManager>();
        }
        return instance;
    }

    public static void LoadMusic(AudioSource audioSource, string fileName, Action<bool> callBack)
	{
		AssetBundleManager.LoadAudio("Sound/" + fileName, (clip)=> {
			audioSource.clip = clip;
			callBack?.Invoke(clip != null);
		});
	}

    public static void LoadEffect(AudioSource audioSource, string fileName, Action<bool> callBack)
    {
        AssetBundleManager.LoadAudio("Sound/" + fileName, (clip) => {
            audioSource.clip = clip;
            callBack?.Invoke(clip != null);
        });
    }
}
