Shader "UI/PuzzlePieceMaskedWithBevel" // Renamed for clarity
{
    Properties
    {
        [PerRendererData] _MainTex ("Sprite Texture", 2D) = "white" {}
        _MaskTex ("Mask Texture", 2D) = "white" {}
        _Color ("Tint", Color) = (1,1,1,1)
        _MaskOffset ("Mask Offset", Vector) = (0,0,0,0)
        _MainTexUV ("Main Texture UV", Vector) = (0,0,1,1)

        // Bevel Properties
        _BevelWidth ("Bevel Width", Range(0, 10)) = 2.0 // How many pixels wide the bevel effect is
        _BevelStrength ("Bevel Strength", Range(0, 2)) = 1.0 // Intensity of the bevel
        _HighlightColor ("Highlight Color", Color) = (1,1,1,0.5) // Color and opacity of highlight
        _ShadowColor ("Shadow Color", Color) = (0,0,0,0.5)    // Color and opacity of shadow

        _StencilComp ("Stencil Comparison", Float) = 8
        _Stencil ("Stencil ID", Float) = 0
        _StencilOp ("Stencil Operation", Float) = 0
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil ReadMask", Float) = 255

        _ColorMask ("Color Mask", Float) = 15

        [Toggle(UNITY_UI_ALPHACLIP)] _UseUIAlphaClip ("Use Alpha Clip", Float) = 0
    }

    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }

        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }

        Cull Off
        Lighting Off
        ZWrite Off
        ZTest [unity_GUIZTestMode]
        Blend SrcAlpha OneMinusSrcAlpha
        ColorMask [_ColorMask]

        Pass
        {
            Name "Default"
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0

            #include "UnityCG.cginc"
            #include "UnityUI.cginc"

            #pragma multi_compile_local _ UNITY_UI_CLIP_RECT
            #pragma multi_compile_local _ UNITY_UI_ALPHACLIP

            struct appdata_t
            {
                float4 vertex   : POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                fixed4 color    : COLOR;
                float2 texcoord  : TEXCOORD0;
                float2 maskcoord : TEXCOORD1;
                float4 worldPosition : TEXCOORD2;
                UNITY_VERTEX_OUTPUT_STEREO
            };

            sampler2D _MainTex;
            sampler2D _MaskTex;
            fixed4 _Color;
            fixed4 _TextureSampleAdd; // Usually (0,0,0,0) unless you are doing specific effects like font textures
            float4 _ClipRect;
            float4 _MainTex_ST;
            // float4 _MaskTex_ST; // Not typically needed for UI if mask uses same UV space as geometry
            float4 _MaskTex_TexelSize; // Will be automatically populated by Unity: (1/width, 1/height, width, height)
            float4 _MaskOffset;
            float4 _MainTexUV;

            // Bevel Properties
            float _BevelWidth;
            float _BevelStrength;
            fixed4 _HighlightColor;
            fixed4 _ShadowColor;

            v2f vert(appdata_t v)
            {
                v2f OUT;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(OUT);
                OUT.worldPosition = v.vertex;
                OUT.vertex = UnityObjectToClipPos(OUT.worldPosition);

                OUT.texcoord = v.texcoord * _MainTexUV.zw + _MainTexUV.xy;
                OUT.maskcoord = v.texcoord + _MaskOffset.xy; // Mask uses original texcoords + offset

                OUT.color = v.color * _Color;
                return OUT;
            }

            fixed4 frag(v2f IN) : SV_Target
            {
                // --- Original Color Sampling ---
                half4 mainTexColor = (tex2D(_MainTex, IN.texcoord) + _TextureSampleAdd) * IN.color;
                half4 mask = tex2D(_MaskTex, IN.maskcoord);

                // Base output color
                fixed4 outputColor = mainTexColor;

                // --- Bevel Calculation ---
                // Only apply bevel if the mask is somewhat opaque
                if (mask.a > 0.01) // Threshold to avoid processing fully transparent areas
                {
                    // Calculate normalized pixel offset for bevel sampling
                    // Use _MaskTex_TexelSize for resolution-independent pixel offsets
                    float2 pixelOffset = _MaskTex_TexelSize.xy * _BevelWidth * 0.5; // 0.5 because we sample on both sides

                    // Sample mask alpha at points above and below the current pixel
                    // In texture coordinates, +Y is often downwards, but Unity UI might be different.
                    // Let's assume +Y in texcoord is "up" on the screen for consistency with "light from top".
                    // If it's inverted, swap alpha_above and alpha_below or negate vertical_diff.
                    float alpha_current = mask.a;
                    float alpha_above = tex2D(_MaskTex, IN.maskcoord + float2(0, pixelOffset.y)).a;
                    float alpha_below = tex2D(_MaskTex, IN.maskcoord - float2(0, pixelOffset.y)).a;

                    // Calculate the vertical "slope" or difference in alpha.
                    // This determines if we're on a "top" or "bottom" facing edge of the inner bevel.
                    // Light from top:
                    // - If alpha_above is less than alpha_current (meaning we are at a top edge of the opaque shape),
                    //   this edge should be shadowed for an inner bevel.
                    // - If alpha_below is less than alpha_current (meaning we are at a bottom edge of the opaque shape),
                    //   this edge should be highlighted for an inner bevel.
                    // A simpler way: diff = (alpha_below - alpha_above).
                    // If diff > 0, means alpha increases downwards -> bottom edge -> highlight.
                    // If diff < 0, means alpha increases upwards   -> top edge    -> shadow.
                    // This effectively makes `vertical_diff` positive for highlight areas and negative for shadow areas.
                    float vertical_diff = (alpha_below - alpha_above);

                    // Modulate by strength
                    float bevel_intensity = vertical_diff * _BevelStrength;

                    // Calculate highlight and shadow factors (ensure they are mutually exclusive)
                    float highlight_factor = saturate(bevel_intensity);  // Only positive diffs contribute
                    float shadow_factor = saturate(-bevel_intensity); // Only negative diffs (inverted) contribute

                    // Apply highlight: Lerp towards highlight color
                    // The alpha of _HighlightColor controls its blend opacity
                    outputColor.rgb = lerp(outputColor.rgb, _HighlightColor.rgb, highlight_factor * _HighlightColor.a);

                    // Apply shadow: Lerp towards shadow color
                    // The alpha of _ShadowColor controls its blend opacity
                    outputColor.rgb = lerp(outputColor.rgb, _ShadowColor.rgb, shadow_factor * _ShadowColor.a);
                }

                // Apply original mask alpha to the final color (including bevel)
                outputColor.a *= mask.a;

                // --- Standard UI Clipping ---
                #ifdef UNITY_UI_CLIP_RECT
                outputColor.a *= UnityGet2DClipping(IN.worldPosition.xy, _ClipRect);
                #endif

                #ifdef UNITY_UI_ALPHACLIP
                clip (outputColor.a - 0.001);
                #endif

                return outputColor;
            }
            ENDCG
        }
    }
}