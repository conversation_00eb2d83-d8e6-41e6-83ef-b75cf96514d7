using UnityEngine;
using System.Collections;
using LitJson;
using System;
using System.Collections.Generic;

public class JsonUtil
{

	public static bool ContainKey(JsonData data, string key)
	{
		if(data == null)
			return false;
		return data.ContainsKey(key);
	}

	public static JsonData ToJson(JsonData data, string key)
    {
		return ContainKey(data, key) ? data[key] : null;
	}

	public static int ToInt(JsonData data)
	{
		if (data == null)
			return 0;
		
		if(data.IsString)
		{
			return StringUtil.ToInt((string)data);
		}
		else if(data.IsInt)
		{
			return (int)data;
		}
		return 0;
	}

	public static int ToInt(JsonData data, string key)
	{
		if(ContainKey(data, key))
		{
			return ToInt (data [key]);
		}
		return 0;
	}

	public static long ToLong(JsonData data)
	{
		if (data == null)
			return 0;

		if (data.IsString)
		{
			return StringUtil.ToLong ((string)data);
		}
		else if (data.IsInt)
		{
			return (long)data;
		}
		else if (data.IsLong)
		{
			return (long)data;
		}
		return 0;
	}

	public static long ToLong(JsonData data, string key)
	{
		if(<PERSON><PERSON><PERSON><PERSON>(data, key))
		{
			return ToLong (data [key]);
		}
		return 0;
	}

    public static float ToFloat(JsonData data)
    {
		if (data == null)
			return 0;

		if (data.IsString)
		{
			return StringUtil.ToFloat((string)data);
		}
		else if (data.IsDouble || data.IsInt || data.IsLong)
		{
			return StringUtil.ToFloat(data.ToString());
		}
		return 0;
	}

    public static float ToFloat(JsonData data, string key)
    {
		if (ContainKey(data, key))
		{
			return ToFloat(data[key]);
		}
		return 0;
	}

	public static double ToDouble(JsonData data)
	{
		if (data == null)
			return 0;

		if (data.IsString)
		{
			return StringUtil.ToDouble ((string)data);
		}
		else if (data.IsDouble || data.IsInt || data.IsLong)
		{
			return (double)data;
		}
		return 0;
	}

	public static double ToDouble(JsonData data, string key)
	{
		if(ContainKey(data, key))
		{
			return ToDouble (data [key]);
		}
		return 0;
	}

	public static bool ToBool(JsonData data)
	{
		if (data == null)
			return false;
		
		if(data.IsString)
		{
			return StringUtil.ToBool((string)data);
		}
        if (data.IsInt)
        {
            return ((int)data != 0);
        }
        else if(data.IsBoolean)
		{
			return (bool)data;
		}
		return false;
	}

	public static bool ToBool(JsonData data, string key)
	{
		if(ContainKey(data, key))
		{
			return ToBool (data [key]);
		}
		return false;
	}

    public static string ToString(JsonData data)
    {
        if (data != null)
        {
            return data.ToString();
        }
        return "";
    }
	public static string ToString(JsonData data, string key)
	{
		if (ContainKey (data, key))
        {
            return ToString(data[key]);
        }
			
		return "";
	}

	public static string[] ToStringArr(JsonData data, string key)
	{
		if (ContainKey (data, key))
		{
			JsonData arr = data [key];
			string[] strs = new string[arr.Count];
			for(int i=0; i<arr.Count; i++)
			{
				strs [i] = arr [i].ToString ();
			}
			return strs;
		}
		return new string[0];
	}

	public static int[] ToIntArr(JsonData data, string key)
	{
		if (ContainKey(data, key))
		{
			JsonData arr = data[key];
			int[] result = new int[arr.Count];
			for (int i = 0; i < arr.Count; i++)
			{
				if (arr[i] == null)
				{
					result[i] = 0;
					continue;
				}
				int.TryParse(arr[i].ToString(), out int value);
				result[i] = value;
			}
			return result;
		}
		return new int[0];
	}

	internal static T ToObject<T>(JsonData data, string key)
	{
		if (ContainKey(data, key))
		{
			JsonData json = data[key];
			return JsonMapper.ToObject<T>(json.ToJson());
		}
		return default(T);
	}
}

