﻿using System;
using System.Collections.Generic;
using UnityEngine;

public class TimerManager : MonoBehaviour
{
    public static TimerManager Create()
    {
        var go = new GameObject("TimerManager");
        return go.AddComponent<TimerManager>();
    }

    private static TimerManager _instance;
    private readonly List<TimerInfo> timers = new List<TimerInfo>(100);
    private readonly List<int> needRemoveTimers = new List<int>(100);
    public static TimerManager Instance
    {
        get
        {
            if (_instance == null)
            {
                var go = new GameObject("TimerManager");
                _instance = go.AddComponent<TimerManager>();
                DontDestroyOnLoad(go);
            }
            return _instance;
        }
    }

    private void Update()
    {
        for (int i = 0; i < timers.Count; i++)
        {
            TimerInfo timer = timers[i];
            timer.elapsedTime += Time.deltaTime;
            if (timer.elapsedTime >= timer.interval)
            {
                timer.action();
                if(timer.repeatCount > 0)
                {
                    timer.repeatCount--;
                }
                timer.elapsedTime -= timer.interval;
            }
        }

        for (int i = timers.Count - 1; i >= 0; i--)
        {
            var timer = timers[i];
            if (timer.repeatCount == 0)
            {
                timers.RemoveAt(i);
            }
        }
    }

    public int DelayCall(float delayTime, Action onAction)
    {
        var timer = new TimerInfo(delayTime, 1, onAction);
        timers.Add(timer);
        return timer.id;
    }

    public int LoopRepeat(float interval, int repeatCount, Action onAction)
    {
        var timer = new TimerInfo(interval, repeatCount, onAction);
        timers.Add(timer);
        return timer.id;
    }

    public int LoopTime(float interval, Action onAction)
    {
        var timer = new TimerInfo(interval, -1, onAction);
        timers.Add(timer);
        return timer.id;
    }
    public int LoopFrame(Action onAction)
    {
        var timer = new TimerInfo(0, -1, onAction);
        timers.Add(timer);
        return timer.id;
    }

    public void Remove(Action onAction)
    {
        for (int i = Instance.timers.Count - 1; i >= 0; i--)
        {
            TimerInfo timer = Instance.timers[i];
            if (timer.action == onAction)
            {
                timer.repeatCount = 0;
                break;
            }
        }
    }

    public void Remove(int timerId)
    {
        if (timerId == 0)
            return;
        for (int i = Instance.timers.Count - 1; i >= 0; i--)
        {
            TimerInfo timer = Instance.timers[i];
            if (timer.id == timerId)
            {
                timer.repeatCount = 0;
                break;
            }
        }
    }

    public void RemoveAll()
    {
        timers.Clear();
    }

    private class TimerInfo
    {
        private static int _TimerID = 0;
        public int id;
        public float interval;
        public int repeatCount;
        public Action action;
        public float elapsedTime;

        public TimerInfo(float interval, int repeatCount, Action action)
        {
            id = ++_TimerID;
            this.interval = interval;
            this.repeatCount = repeatCount;
            this.action = action;
        }

        public bool ShouldRemove()
        {
            return repeatCount == 0;
        }
    }
}
