﻿using FairyGUI;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Pool;

internal class FUILoader
{
    private const string UIPathPrefix = "UI/";

    public static void LoadPackage(string packname, string[] extraPacks, Action doneAction = null, Action<float> onProgress = null, bool needRelease = false)
    {
        if (extraPacks == null)
        {
            LoadPackage(packname, doneAction);
        }
        else
        {
            var tempPackNames = ListPool<string>.Get();
            tempPackNames.Add(packname);
            tempPackNames.AddRange(extraPacks);
            LoadPackages(tempPackNames, doneAction);
            ListPool<string>.Release(tempPackNames);
        }
    }

    public static void LoadPackages(List<string> packNames, Action onComplete)
    {
        var packagesToLoad = packNames.Count;
        if (packagesToLoad == 0)
        {
            onComplete.Invoke();
            return;
        }

        var loadedPackages = 0;
        foreach (var packageName in packNames)
        {
            LoadPackage(packageName, () =>
            {
                loadedPackages++;
                if (loadedPackages == packagesToLoad)
                {
                    onComplete?.Invoke();
                }
            });
        }
    }

    public static void LoadPackage(string packageName, Action onComplete = null)
    {
        if (UIPackage.GetByName(packageName) == null)
        {
            AddPackage(packageName, onComplete);
        }
        else
        {
            onComplete?.Invoke();
        }
    }

    private static void AddPackage(string packageName, Action onComplete)
    {
        string packageFilePath = $"{UIPathPrefix}{packageName}/{packageName}_fui";
        AssetBundleManager.LoadBytes(packageFilePath, bytes =>
        {
            if (bytes == null)
                return;
            List<LoadInfo> loadInfos = new List<LoadInfo>();
            UIPackage package = AddPackageFromBytes(packageName, bytes, loadInfos);
            PrepareLoadList(packageName, package, loadInfos);

            LoadAssets(loadInfos, onComplete);
        });
    }

    private static UIPackage AddPackageFromBytes(string packageName, byte[] bytes, List<LoadInfo> loadInfos)
    {
        UIPackage package = UIPackage.AddPackage(bytes, packageName, (name, extension, type, item) =>
        {
            if (type == typeof(Texture))
            {
                var texturePath = $"{UIPathPrefix}{packageName}/{name}";
                loadInfos.Add(new LoadInfo(texturePath, item));
            }
        });

        return package;
    }

    private static List<LoadInfo> PrepareLoadList(string packageName, UIPackage package, List<LoadInfo> loadInfos)
    {
        var items = package.GetItems();
        foreach (var item in items)
        {
            if (item.type == PackageItemType.Spine && item.skeletonAsset == null)
            {
                string skeletonDataPath = $"{UIPathPrefix}{packageName}/{item.name}_SkeletonData";
                loadInfos.Add(new LoadInfo(skeletonDataPath, item));
            }
            else
            {
                package.GetItemAsset(item);
            }
        }

        return loadInfos;
    }

    private static void LoadAssets(List<LoadInfo> loadInfos, Action onComplete)
    {
        int loadedCount = 0;

        if (loadInfos.Count == 0)
        {
            onComplete?.Invoke();
            return;
        }

        foreach (var loadInfo in loadInfos)
        {
            AssetBundleManager.LoadObject(loadInfo.Path, obj =>
            {
                loadInfo.Item.owner.SetItemAsset(loadInfo.Item, obj, DestroyMethod.None);
                loadedCount++;
                if (loadedCount == loadInfos.Count)
                {
                    onComplete?.Invoke();
                }
            });
        }
    }

    protected class LoadInfo
    {
        public string Path { get; }
        public PackageItem Item { get; }

        public LoadInfo(string path, PackageItem item)
        {
            Path = path;
            Item = item;
        }
    }
}