﻿using System;
using System.Collections;
using UnityEngine;

public class BackgroundSound : MonoBehaviour {

    public const float DEFAULT_VOLUME = 0.7f;

    public string path = "";
	private AudioSource audioSource;
    private Action _callback = null;

    void Awake () 
    {
		audioSource = gameObject.AddComponent<AudioSource>();
	}

    public bool Mute
    {
        get
        {
            return GetVolume().Equals(0);
        }
        set
        {
            SetVolume(value ? 0 : 1f);
        }
    }

    public void SetVolume(float volume)
    {
		audioSource.volume = volume;
    }

	public float GetVolume()
	{
		return audioSource.volume;
	}

	public void Play(string path, bool loop = true, Action callBack = null)
	{
        if (this.path.Equals(path) || string.IsNullOrEmpty(path))
            return;

        this.path = path;
        _callback = callBack;
        SoundManager.LoadMusic(audioSource, path, (bool success) => {
            audioSource.Play();
            if (_callback != null)
            {
                StartCoroutine(AudioPlayFinished(audioSource.clip.length, _callback));
            }
        });
        audioSource.time = 0;
        audioSource.loop = loop;
    }


    private IEnumerator AudioPlayFinished(float time, Action callback)
    {
        yield return new WaitForSeconds(time);
        _callback?.Invoke();
    }

    public void Play(string path, float startTime = 0, bool loop = true)
	{
        if (this.path.Equals(path) || string.IsNullOrEmpty(path))
        {
            if (!audioSource.isPlaying)
            {
                Play();
            }
            return;
        }

        this.path = path;
        SoundManager.LoadMusic(audioSource, path, (bool success) => {
            audioSource.time = startTime;
            audioSource.Play();
        });
        audioSource.loop = loop;
    }

    public void Stop()
    {
        audioSource.Stop();
    }
    public void Play()
    {
        audioSource.Play();
    }
}
