using UnityEngine;
using UnityEditor;
using System;
using System.IO;
using System.Collections.Generic;
using System.Text;

namespace JigsawGenerator
{
    // 坐标系统说明：
    // - SVG坐标系：(0,0)在左上角，y向下增加
    // - Unity坐标系：(0,0)在左下角，y向上增加
    // - 预览窗口中的拼图片编号已修正为Unity坐标系，与GridSystem保持一致

    public class JigsawGeneratorWindow : EditorWindow
    {
        // 拼图参数
        private int seed = 0;
        private float tabSize = 20f;
        private float jitter = 4f;
        private float cornerRadius = 2f;
        private int tilesX = 15;
        private int tilesY = 10;
        private float width = 300f;
        private float height = 200f;

        // 预览
        private Texture2D previewTexture;
        private bool showPreview = true;
        private Vector2 scrollPosition;

        // 生成的拼图数据
        private string svgData;
        private List<JigsawPiece> jigsawPieces;

        [MenuItem("Tools/Jigsaw Generator")]
        public static void ShowWindow()
        {
            var window = GetWindow<JigsawGeneratorWindow>("拼图生成器");
            window.minSize = new Vector2(400, 600);
            window.Show();
        }

        // EditorPrefs键名
        private const string PREF_SEED = "JigsawGenerator_Seed";
        private const string PREF_TAB_SIZE = "JigsawGenerator_TabSize";
        private const string PREF_JITTER = "JigsawGenerator_Jitter";
        private const string PREF_CORNER_RADIUS = "JigsawGenerator_CornerRadius";
        private const string PREF_TILES_X = "JigsawGenerator_TilesX";
        private const string PREF_TILES_Y = "JigsawGenerator_TilesY";
        private const string PREF_WIDTH = "JigsawGenerator_Width";
        private const string PREF_HEIGHT = "JigsawGenerator_Height";

        private void OnEnable()
        {
            // 从EditorPrefs加载上次的设置
            if (EditorPrefs.HasKey(PREF_SEED))
                seed = EditorPrefs.GetInt(PREF_SEED);
            else
                seed = UnityEngine.Random.Range(0, 10000);

            if (EditorPrefs.HasKey(PREF_TAB_SIZE))
                tabSize = EditorPrefs.GetFloat(PREF_TAB_SIZE);

            if (EditorPrefs.HasKey(PREF_JITTER))
                jitter = EditorPrefs.GetFloat(PREF_JITTER);

            if (EditorPrefs.HasKey(PREF_CORNER_RADIUS))
                cornerRadius = EditorPrefs.GetFloat(PREF_CORNER_RADIUS);

            if (EditorPrefs.HasKey(PREF_TILES_X))
                tilesX = EditorPrefs.GetInt(PREF_TILES_X);

            if (EditorPrefs.HasKey(PREF_TILES_Y))
                tilesY = EditorPrefs.GetInt(PREF_TILES_Y);

            if (EditorPrefs.HasKey(PREF_WIDTH))
                width = EditorPrefs.GetFloat(PREF_WIDTH);

            if (EditorPrefs.HasKey(PREF_HEIGHT))
                height = EditorPrefs.GetFloat(PREF_HEIGHT);

            // 创建预览纹理
            UpdatePreviewTexture();
        }

        // 保存设置到EditorPrefs
        private void SaveSettings()
        {
            EditorPrefs.SetInt(PREF_SEED, seed);
            EditorPrefs.SetFloat(PREF_TAB_SIZE, tabSize);
            EditorPrefs.SetFloat(PREF_JITTER, jitter);
            EditorPrefs.SetFloat(PREF_CORNER_RADIUS, cornerRadius);
            EditorPrefs.SetInt(PREF_TILES_X, tilesX);
            EditorPrefs.SetInt(PREF_TILES_Y, tilesY);
            EditorPrefs.SetFloat(PREF_WIDTH, width);
            EditorPrefs.SetFloat(PREF_HEIGHT, height);
        }

        private void OnGUI()
        {
            EditorGUILayout.BeginVertical();

            EditorGUILayout.LabelField("拼图参数", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // 参数设置
            EditorGUI.BeginChangeCheck();

            seed = EditorGUILayout.IntSlider("随机种子", seed, 0, 9999);
            tabSize = EditorGUILayout.Slider("拼片凸起大小", tabSize, 10f, 30f);
            jitter = EditorGUILayout.Slider("随机抖动", jitter, 0f, 13f);
            cornerRadius = EditorGUILayout.FloatField("边角圆角半径 (像素)", cornerRadius);

            EditorGUILayout.BeginHorizontal();
            tilesX = EditorGUILayout.IntField("拼片数量 X", tilesX);
            tilesY = EditorGUILayout.IntField("Y", tilesY);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            width = EditorGUILayout.FloatField("拼图尺寸 宽 (像素)", width);
            height = EditorGUILayout.FloatField("高 (像素)", height);
            EditorGUILayout.EndHorizontal();

            if (EditorGUI.EndChangeCheck())
            {
                UpdatePreviewTexture();
                SaveSettings(); // 保存设置
            }

            EditorGUILayout.Space();

            // 生成按钮
            EditorGUILayout.BeginHorizontal();
            // if (GUILayout.Button("生成SVG"))
            // {
            //     GenerateSVG();
            // }

            // if (GUILayout.Button("生成拼图Prefab"))
            // {
            //     GenerateJigsawPrefab();
            // }
            EditorGUILayout.EndHorizontal();

            // 导出所有拼块按钮
            if (GUILayout.Button("导出所有拼块"))
            {
                ExportIndividualPieces();
            }

            EditorGUILayout.Space();

            // 预览区域
            showPreview = EditorGUILayout.Foldout(showPreview, "预览");
            if (showPreview && previewTexture != null)
            {
                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
                float aspectRatio = (float)previewTexture.width / previewTexture.height;
                float previewWidth = EditorGUIUtility.currentViewWidth - 30;
                float previewHeight = previewWidth / aspectRatio;

                Rect previewRect = GUILayoutUtility.GetRect(previewWidth, previewHeight);
                EditorGUI.DrawPreviewTexture(previewRect, previewTexture);

                EditorGUILayout.EndScrollView();
            }

            EditorGUILayout.EndVertical();
        }

        private void UpdatePreviewTexture()
        {
            int previewWidth = 900;
            int previewHeight = 600;

            if (previewTexture == null || previewTexture.width != previewWidth || previewTexture.height != previewHeight)
            {
                if (previewTexture != null)
                {
                    DestroyImmediate(previewTexture);
                }
                previewTexture = new Texture2D(previewWidth, previewHeight);
            }

            // 清空纹理为编辑器背景色
            Color editorBackgroundColor = EditorGUIUtility.isProSkin ? new Color32(56, 56, 56, 255) : new Color32(194, 194, 194, 255);
            Color[] colors = new Color[previewWidth * previewHeight];
            for (int i = 0; i < colors.Length; i++)
            {
                colors[i] = editorBackgroundColor;
            }
            previewTexture.SetPixels(colors);

            // 生成SVG数据
            string svgData = JigsawGenerator.GenerateSVG(seed, tabSize, jitter, cornerRadius, tilesX, tilesY, width, height);

            // 解析SVG路径
            string horizontalPathData = ExtractPathData(svgData, 0);
            string verticalPathData = ExtractPathData(svgData, 1);
            string borderPathData = ExtractPathData(svgData, 2);

            if (!string.IsNullOrEmpty(horizontalPathData) &&
                !string.IsNullOrEmpty(verticalPathData) &&
                !string.IsNullOrEmpty(borderPathData))
            {
                // 计算缩放和偏移以在预览纹理中正确显示SVG内容并保持宽高比
                float svgActualWidth = this.width;
                float svgActualHeight = this.height;

                if (svgActualWidth <= 0 || svgActualHeight <= 0) // 防止除以零
                {
                    previewTexture.Apply();
                    return;
                }

                float textureAspectRatio = (float)previewWidth / previewHeight;
                float svgAspectRatio = svgActualWidth / svgActualHeight;

                float finalScale;
                float drawOffsetX = 0;
                float drawOffsetY = 0;

                if (textureAspectRatio > svgAspectRatio) // 纹理比SVG更宽，以高度为准进行缩放
                {
                    finalScale = (float)previewHeight / svgActualHeight;
                    drawOffsetX = ((float)previewWidth - (svgActualWidth * finalScale)) / 2f;
                }
                else // 纹理比SVG更高或宽高比相同，以宽度为准进行缩放
                {
                    finalScale = (float)previewWidth / svgActualWidth;
                    drawOffsetY = ((float)previewHeight - (svgActualHeight * finalScale)) / 2f;
                }

                // 在绘制路径前，先将拼图区域填充为白色
                int fillX = Mathf.RoundToInt(drawOffsetX);
                int fillY = Mathf.RoundToInt(drawOffsetY);
                int fillWidth = Mathf.RoundToInt(svgActualWidth * finalScale);
                int fillHeight = Mathf.RoundToInt(svgActualHeight * finalScale);

                // 确保填充区域在纹理范围内
                fillX = Mathf.Max(0, fillX);
                fillY = Mathf.Max(0, fillY);
                fillWidth = Mathf.Min(previewWidth - fillX, fillWidth);
                fillHeight = Mathf.Min(previewHeight - fillY, fillHeight);

                if (fillWidth > 0 && fillHeight > 0)
                {
                    Color[] fillColors = new Color[fillWidth * fillHeight];
                    for (int i = 0; i < fillColors.Length; i++)
                    {
                        fillColors[i] = Color.white;
                    }
                    // Texture2D.SetPixels(x, y, blockWidth, blockHeight, colors[])
                    // Y 轴需要注意，SetPixels的y是从下往上，而我们的drawOffsetY是从上往下计算的。
                    // 不过，由于我们是填充整个矩形，并且背景已经是编辑器颜色，
                    // 这里的Y坐标应该是相对于纹理底部的。
                    // drawOffsetY是顶部空白的高度，所以实际填充的Y起点是 drawOffsetY。
                    // 但是SetPixels的Y是从底部开始的。
                    // 预览纹理的高度是 previewHeight。
                    // 填充区域的底部Y坐标是 drawOffsetY + fillHeight (从顶部算)
                    // 转换为从底部算的Y坐标是 previewHeight - (drawOffsetY + fillHeight)
                    // 但由于我们已经将整个纹理背景设置为编辑器颜色，
                    // 这里的填充应该是针对拼图内容区域，所以Y坐标应该是从 drawOffsetY 开始，持续 fillHeight。
                    // SetPixels 的 y 参数是从纹理的底部开始计数的。
                    // 我们计算的 drawOffsetY 是从纹理顶部开始的偏移。
                    // 所以，填充区域的底部在纹理中的y坐标是 (previewHeight - (drawOffsetY + fillHeight))
                    // 但这似乎过于复杂了，因为我们已经将整个纹理背景设置为编辑器颜色。
                    // 我们需要填充的区域是 (drawOffsetX, drawOffsetY) 到 (drawOffsetX + fillWidth, drawOffsetY + fillHeight)
                    // SetPixels(int x, int y, int blockWidth, int blockHeight, Color[] colors)
                    // y is the y-coordinate of the bottom-left corner of the block.
                    // 我们的 drawOffsetY 是从顶部开始的。
                    // 所以，填充区域的 bottom-left y 应该是 previewHeight - (drawOffsetY + fillHeight)
                    // 但考虑到我们已经将整个背景设置为编辑器颜色，我们只需要确保拼图区域是白色。
                    // 填充的Y起点应该是 drawOffsetY （从顶部算），高度是 fillHeight。
                    // SetPixels 的 Y 是从底部开始的。
                    // 假设 drawOffsetY 是从顶部开始的偏移。
                    // 填充区域的顶部Y是 drawOffsetY，底部Y是 drawOffsetY + fillHeight。
                    // 对应到SetPixels的Y，应该是 (previewHeight - (drawOffsetY + fillHeight))
                    // 这有点混乱。让我们简化：直接遍历像素并设置。
                    for (int py = 0; py < fillHeight; py++)
                    {
                        for (int px = 0; px < fillWidth; px++)
                        {
                            // SetPixel的y是从底部开始的。
                            // 我们要填充的区域的顶部y是 drawOffsetY (相对于纹理顶部)
                            // 我们要填充的区域的当前行y是 drawOffsetY + py (相对于纹理顶部)
                            // 转换为SetPixel的y: previewHeight - 1 - (drawOffsetY + py)
                            int targetY = Mathf.FloorToInt(previewHeight - 1 - (drawOffsetY + py));
                            int targetX = Mathf.FloorToInt(drawOffsetX + px);
                            if (targetX >= 0 && targetX < previewWidth && targetY >= 0 && targetY < previewHeight)
                            {
                                previewTexture.SetPixel(targetX, targetY, Color.white);
                            }
                        }
                    }
                }

                // 绘制路径
                DrawPath(horizontalPathData, Color.blue, previewWidth, previewHeight, svgActualWidth, svgActualHeight, drawOffsetX, drawOffsetY, finalScale);
                DrawPath(verticalPathData, Color.red, previewWidth, previewHeight, svgActualWidth, svgActualHeight, drawOffsetX, drawOffsetY, finalScale);
                DrawPath(borderPathData, Color.black, previewWidth, previewHeight, svgActualWidth, svgActualHeight, drawOffsetX, drawOffsetY, finalScale);

                // 绘制拼图片的边界
                DrawPieceGrid(previewWidth, previewHeight, svgActualWidth, svgActualHeight, drawOffsetX, drawOffsetY, finalScale);

                // 绘制拼图片的编号
                DrawPieceNumbers(previewWidth, previewHeight, svgActualWidth, svgActualHeight, drawOffsetX, drawOffsetY, finalScale);
            }

            previewTexture.Apply();
        }

        // 绘制拼图片的网格
        private void DrawPieceGrid(int textureWidth, int textureHeight, float svgOriginalWidth, float svgOriginalHeight, float textureDrawOffsetX, float textureDrawOffsetY, float scaleFactor)
        {
            if (tilesX <= 0 || tilesY <= 0) return; // 防止除以零

            float pieceSvgWidth = svgOriginalWidth / tilesX;
            float pieceSvgHeight = svgOriginalHeight / tilesY;

            for (int i = 0; i <= tilesX; i++) // Renamed x to i to avoid conflict
            {
                float xPosOnSvg = i * pieceSvgWidth;
                float currentXPosOnTexture = xPosOnSvg * scaleFactor + textureDrawOffsetX;
                DrawLine(
                    new Vector2(currentXPosOnTexture, textureDrawOffsetY), // Use textureDrawOffsetY for the start of Y
                    new Vector2(currentXPosOnTexture, svgOriginalHeight * scaleFactor + textureDrawOffsetY), // Use scaled height for the end of Y
                    new Color(0.7f, 0.7f, 0.7f, 0.5f)
                );
            }

            float scaledTextureHeight = svgOriginalHeight * scaleFactor; // Cache this value
            for (int i = 0; i <= tilesY; i++) // Renamed y to i to avoid conflict
            {
                float yPosOnSvg = i * pieceSvgHeight;
                // Y轴反转和偏移
                float currentYPosOnTexture = scaledTextureHeight - (yPosOnSvg * scaleFactor) + textureDrawOffsetY;
                DrawLine(
                    new Vector2(textureDrawOffsetX, currentYPosOnTexture), // Use textureDrawOffsetX for the start of X
                    new Vector2(svgOriginalWidth * scaleFactor + textureDrawOffsetX, currentYPosOnTexture), // Use scaled width for the end of X
                    new Color(0.7f, 0.7f, 0.7f, 0.5f)
                );
            }
        }

        // 绘制拼图片的编号
        private void DrawPieceNumbers(int textureWidth, int textureHeight, float svgOriginalWidth, float svgOriginalHeight, float textureDrawOffsetX, float textureDrawOffsetY, float scaleFactor)
        {
            if (tilesX <= 0 || tilesY <= 0) return; // 防止除以零

            float pieceSvgWidth = svgOriginalWidth / tilesX;
            float pieceSvgHeight = svgOriginalHeight / tilesY;

            float scaledTextureHeight = svgOriginalHeight * scaleFactor;

            for (int y = 0; y < tilesY; y++)
            {
                for (int x = 0; x < tilesX; x++)
                {
                    float xPosOnSvgCenter = (x + 0.5f) * pieceSvgWidth;
                    float yPosOnSvgCenter = (y + 0.5f) * pieceSvgHeight;

                    float xPosOnTexture = xPosOnSvgCenter * scaleFactor + textureDrawOffsetX;
                    // Y轴反转：SVG原点在左上，Texture原点在左下。
                    // 1. 将SVG Y坐标乘以缩放因子。
                    // 2. 从缩放后的纹理高度中减去它，得到相对于纹理底部的Y。
                    // 3. 加上Y方向的偏移。
                    float yPosOnTexture = scaledTextureHeight - (yPosOnSvgCenter * scaleFactor) + textureDrawOffsetY;

                    // 绘制简单的数字标记
                    // 使用Unity坐标系计算拼图片编号：底部为y=0
                    int unityY = tilesY - 1 - y; // 转换为Unity坐标系
                    int pieceNumber = unityY * tilesX + x + 1;
                    string numberText = pieceNumber.ToString();

                    // 这里只是简单地在中心位置绘制一个点
                    for (int i = -1; i <= 1; i++)
                    {
                        for (int j = -1; j <= 1; j++)
                        {
                            SetPixel((int)xPosOnTexture + i, (int)yPosOnTexture + j, Color.black);
                        }
                    }
                }
            }
        }

        private string ExtractPathData(string svgData, int pathIndex)
        {
            System.Text.RegularExpressions.Match match = System.Text.RegularExpressions.Regex.Match(
                svgData,
                "<path[^>]*d=\"([^\"]*)\"[^>]*>"
            );

            int currentIndex = 0;
            while (match.Success)
            {
                if (currentIndex == pathIndex)
                {
                    return match.Groups[1].Value;
                }

                currentIndex++;
                match = match.NextMatch();
            }

            return null;
        }

        private void DrawPath(string pathData, Color color, int textureWidth, int textureHeight, float svgOriginalWidth, float svgOriginalHeight, float textureDrawOffsetX, float textureDrawOffsetY, float scaleFactor)
        {
            List<List<Vector2>> paths = SVGParser.ParseSVGPath(pathData);
            if (svgOriginalWidth <= 0 || svgOriginalHeight <= 0) return; // 防止除以零

            float scaledTextureHeight = svgOriginalHeight * scaleFactor;

            foreach (List<Vector2> path in paths)
            {
                if (path.Count < 2)
                    continue;

                for (int i = 0; i < path.Count - 1; i++)
                {
                    Vector2 startSvg = path[i];
                    Vector2 endSvg = path[i + 1];

                    // 将SVG坐标转换为纹理坐标，考虑缩放和偏移，并反转Y轴
                    Vector2 startPixel = new Vector2(
                        startSvg.x * scaleFactor + textureDrawOffsetX,
                        scaledTextureHeight - (startSvg.y * scaleFactor) + textureDrawOffsetY
                    );

                    Vector2 endPixel = new Vector2(
                        endSvg.x * scaleFactor + textureDrawOffsetX,
                        scaledTextureHeight - (endSvg.y * scaleFactor) + textureDrawOffsetY
                    );

                    DrawLine(startPixel, endPixel, color);
                }
            }
        }

        private void DrawLine(Vector2 start, Vector2 end, Color color)
        {
            int x0 = Mathf.RoundToInt(start.x);
            int y0 = Mathf.RoundToInt(start.y);
            int x1 = Mathf.RoundToInt(end.x);
            int y1 = Mathf.RoundToInt(end.y);

            bool steep = Mathf.Abs(y1 - y0) > Mathf.Abs(x1 - x0);
            if (steep)
            {
                int temp = x0;
                x0 = y0;
                y0 = temp;

                temp = x1;
                x1 = y1;
                y1 = temp;
            }

            if (x0 > x1)
            {
                int temp = x0;
                x0 = x1;
                x1 = temp;

                temp = y0;
                y0 = y1;
                y1 = temp;
            }

            int dx = x1 - x0;
            int dy = Mathf.Abs(y1 - y0);
            int error = dx / 2;
            int ystep = (y0 < y1) ? 1 : -1;
            int y = y0;

            for (int x = x0; x <= x1; x++)
            {
                if (steep)
                {
                    SetPixel(y, x, color);
                }
                else
                {
                    SetPixel(x, y, color);
                }

                error -= dy;
                if (error < 0)
                {
                    y += ystep;
                    error += dx;
                }
            }
        }

        private void SetPixel(int x, int y, Color color)
        {
            if (x >= 0 && x < previewTexture.width && y >= 0 && y < previewTexture.height)
            {
                previewTexture.SetPixel(x, y, color);
            }
        }

        private void GenerateSVG()
        {
            // 生成SVG数据
            svgData = JigsawGenerator.GenerateSVG(seed, tabSize, jitter, cornerRadius, tilesX, tilesY, width, height);

            // 保存当前设置
            SaveSettings();

            // 保存SVG文件
            string path = EditorUtility.SaveFilePanel("保存SVG文件", "", "jigsaw.svg", "svg");
            if (!string.IsNullOrEmpty(path))
            {
                File.WriteAllText(path, svgData);
                Debug.Log("SVG文件已保存到: " + path);
            }
        }

        private void GenerateJigsawPrefab()
        {
            // 生成SVG数据
            svgData = JigsawGenerator.GenerateSVG(seed, tabSize, jitter, cornerRadius, tilesX, tilesY, width, height);

            // 保存当前设置
            SaveSettings();

            // 生成拼图片段
            jigsawPieces = JigsawMeshGenerator.GenerateJigsawPieces(svgData, tilesX, tilesY);

            if (jigsawPieces == null || jigsawPieces.Count == 0)
            {
                Debug.LogError("生成拼图片段失败");
                return;
            }

            // 选择图片纹理（可选）
            Texture2D texture = null;
            bool useTexture = EditorUtility.DisplayDialog("使用纹理", "是否为拼图添加图片纹理？", "选择图片", "使用随机颜色");

            if (useTexture)
            {
                string texturePath = EditorUtility.OpenFilePanel("选择图片", "", "png,jpg,jpeg");
                if (!string.IsNullOrEmpty(texturePath))
                {
                    byte[] fileData = File.ReadAllBytes(texturePath);
                    texture = new Texture2D(2, 2);
                    texture.LoadImage(fileData);
                }
            }

            // 保存Prefab
            string prefabPath = EditorUtility.SaveFilePanelInProject("保存拼图Prefab", "JigsawPuzzle", "prefab", "请选择保存Prefab的位置");
            if (!string.IsNullOrEmpty(prefabPath))
            {
                JigsawMeshGenerator.CreateJigsawPrefab(jigsawPieces, prefabPath, texture);
            }
        }

        private void ExportIndividualPieces()
        {
            // 保存当前设置
            SaveSettings();

            // 选择保存目录
            string folderPath = EditorUtility.SaveFolderPanel("选择保存拼块的文件夹", "", "");
            if (string.IsNullOrEmpty(folderPath))
                return;

            // 生成所有拼块的SVG数据
            Dictionary<string, string> piecesData = JigsawGenerator.GenerateAllPieces(
                seed, tabSize, jitter, cornerRadius, tilesX, tilesY, width, height);

            if (piecesData.Count == 0)
            {
                Debug.LogError("生成拼块数据失败");
                return;
            }

            // 保存每个拼块到单独的SVG文件
            int successCount = 0;
            foreach (var piece in piecesData)
            {
                string filePath = Path.Combine(folderPath, piece.Key);
                try
                {
                    File.WriteAllText(filePath, piece.Value);
                    successCount++;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"保存拼块 {piece.Key} 失败: {ex.Message}");
                }
            }

            Debug.Log($"成功导出 {successCount}/{piecesData.Count} 个拼块到: {folderPath}");

            // 打开保存目录
            EditorUtility.RevealInFinder(folderPath);
        }
    }
}
