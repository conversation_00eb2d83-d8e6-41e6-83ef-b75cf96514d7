using UnityEngine;
using System.Collections.Generic;

public class URLRequestData
{
    private List<KeyValuePair<string, object>> data;
    public URLRequestData()
    {
        data = new List<KeyValuePair<string, object>>();
    }

    public void Add(string key, object value)
    {
        data.Add(new KeyValuePair<string, object>(key, value));
    }

    public bool HasData()
    {
        return data.Count > 0;
    }

    public string GetDataString()
    {
        string result;
        string[] strArr = new string[data.Count];
        for (int i = 0; i < data.Count; i++)
        {
            KeyValuePair<string, object> kvp = data[i];
            strArr[i] = kvp.Key + "=" + kvp.Value;
        }
        result = string.Join("&", strArr);
        return result;
    }

    public WWWForm GetDataForm()
    {
        WWWForm form = new WWWForm();
        for (int i = 0; i < data.Count; i++)
        {
            KeyValuePair<string, object> kvp = data[i];
            form.AddField(kvp.Key, kvp.Value.ToString());
        }
        return form;
    }
}

