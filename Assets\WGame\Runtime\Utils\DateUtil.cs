using System.Text;

public class DateUtil
{
    private static StringBuilder sb = new StringBuilder();

    /// <summary>
    /// 分钟:秒
    /// </summary>
    /// <param name="time"></param>
    /// <returns></returns>
    public static string MinuteSecond(float time)
    {
        int minute = (int)(time / 60);
        int second = (int)(time % 60);
        sb.Clear();
        sb.Append(minute.ToString("00"));
        sb.Append(":");
        sb.Append(second.ToString("00"));
        return sb.ToString();
    }


    /// <summary>
    /// 秒
    /// </summary>
    /// <param name="time"></param>
    /// <returns></returns>
    public static string Second(int time)
    {
        int second = time;
        sb.Clear();
        sb.Append(second.ToString("00"));
        return sb.ToString();
    }

    /// <summary>
    /// 小时:分钟
    /// </summary>
    /// <param name="time"></param>
    /// <returns></returns>
    public static string HourMinute(int time)
    {
        int hour = time / 3600;
        int minute = (time % 3600) / 60;
        sb.Clear();
        sb.Append(hour.ToString("00"));
        sb.Append(":");
        sb.Append(minute.ToString("00"));
        return sb.ToString();
    }

    /// <summary>
    /// 小时:分钟:秒
    /// </summary>
    /// <param name="time"></param>
    /// <returns></returns>
    public static string HourMinuteSecond(int time)
    {
        int hour = time / 3600;
        int minute = (time % 3600) / 60;
        int second = time % 60;
        sb.Clear();
        sb.Append(hour.ToString("00"));
        sb.Append(":");
        sb.Append(minute.ToString("00"));
        sb.Append(":");
        sb.Append(second.ToString("00"));
        return sb.ToString();
    }
}