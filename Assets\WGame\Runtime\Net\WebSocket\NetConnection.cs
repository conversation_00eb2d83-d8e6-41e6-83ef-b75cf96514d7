﻿using Google.Protobuf;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityWebSocket;


public class NetConnection : MonoBehaviour
{
    public static uint MsgId = 0;
    public Action<NetMessage> OnMessageReceived;
    public Action<ESocketState> OnSocketStateChange;
    public string address = "ws://127.0.0.1:8888";
    private IWebSocket webSocket;
    private List<NetMessage> unsentMsgs = new List<NetMessage>();
    private void Awake()
    {

    }

    public void Connect()
    {
        Dispose();
        this.webSocket = new WebSocket(address);
        this.webSocket.OnOpen += OnOpen;
        this.webSocket.OnMessage += OnMessage;
        this.webSocket.OnClose += OnClosed;
        this.webSocket.OnError += OnError;
        this.webSocket.ConnectAsync();
    }

    public bool IsOpen
    {
        get
        {
            WebSocketState state = webSocket == null ? WebSocketState.Closed : webSocket.ReadyState;
            return state == WebSocketState.Open;
        }
    }

    void OnOpen(object sender, OpenEventArgs e)
    {
        MsgId = 0;
        UnityEngine.Debug.Log("WebSocket Open!");
        OnSocketStateChange.Invoke(ESocketState.Opened);
    }

    void OnMessage(object sender, MessageEventArgs e)
    {
        try
        {
            NetMessage msg;
            if (e.IsBinary)
            {
                msg = NetMessage.FromArray(e.RawData);
            }
            else
            {
                msg = NetMessage.FromString(e.Data);
            }

            OnMessageReceived.Invoke(msg);
        }
        catch (Exception err)
        {
            Debug.LogException(err);
        }
    }

    /// <summary>
    /// Called when the web socket closed
    /// </summary>
    void OnClosed(object sender, CloseEventArgs e)
    {
        UnityEngine.Debug.Log(string.Format("Closed: StatusCode: {0}, Reason: {1}", e.StatusCode, e.Reason));
        OnSocketStateChange?.Invoke(ESocketState.Closed);
    }

    /// <summary>
    /// Called when an error occured on client side
    /// </summary>
    void OnError(object sender, ErrorEventArgs e)
    {
        UnityEngine.Debug.Log(string.Format("An error occured: <color=red>{0}</color>", e.Message));
        OnSocketStateChange?.Invoke(ESocketState.Error);
    }


    public void ResendMsgs()
    {
        if (!IsOpen)
        {
            Debug.LogWarning("[NetConnection] Failed to resend message");
            return;
        }
        for (int i = 0; i < unsentMsgs.Count; i++)
        {
            var msg = unsentMsgs[i];
            msg.msgIdx = ++MsgId;//重置msgid
            webSocket.SendAsync(msg.ToArray());
        }
        unsentMsgs.Clear();
    }

    public void ClearUnsentMsgs()
    {
        unsentMsgs.Clear();
    }

    public void Send(int cmd, IMessage content = null)
    {
        var msg = new NetMessage()
        {
            cmd = (uint)cmd,
            msgIdx = ++MsgId,
            data = content == null ? null : content.ToByteArray()
        };

        if (!IsOpen)
        {
            unsentMsgs.Add(msg);
            return;
        }

#if UNITY_EDITOR
        // if (cmd != NetMsg.Ping)
        // {
        //     Log.Debug($"[Socket] <color=#00ff00>{cmd}</color> {content}");
        // }
#endif
        webSocket.SendAsync(msg.ToArray());
    }

    public void Disconnect()
    {
        //Debuger.Log("WebSocket Disconnect");
        ClearUnsentMsgs();
        this.webSocket?.CloseAsync();
    }

    public void Dispose()
    {
        if (this.webSocket != null)
        {
            ClearUnsentMsgs();
            MsgId = 0;
            this.webSocket.OnOpen -= OnOpen;
            this.webSocket.OnMessage -= OnMessage;
            this.webSocket.OnClose -= OnClosed;
            this.webSocket.OnError -= OnError;
            if (this.webSocket.ReadyState != WebSocketState.Closed) this.webSocket.CloseAsync();
            this.webSocket = null;
        }
    }

    private static NetConnection instance;
    public static NetConnection GetInstance()
    {
        if (instance == null)
        {
            GameObject gameObject = new GameObject("NetConnection");
            instance = gameObject.AddComponent<NetConnection>();
        }
        return instance;
    }
}
