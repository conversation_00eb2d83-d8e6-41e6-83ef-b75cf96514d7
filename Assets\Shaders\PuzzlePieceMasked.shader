Shader "UI/PuzzlePieceMasked2"
{
    Properties
    {
        [PerRendererData] _MainTex ("Sprite Texture", 2D) = "white" {}
        // _MainTex ("Sprite Texture", 2D) = "white" {}
        _MaskTex ("Mask Texture", 2D) = "white" {}
        _Color ("Tint", Color) = (1,1,1,1)
        _MaskOffset ("Mask Offset", Vector) = (0,0,0,0)
        _MainTexUV ("Main Texture UV", Vector) = (0,0,1,1)
        _MaskTexUV ("Mask Texture UV", Vector) = (0,0,1,1)

        _StencilComp ("Stencil Comparison", Float) = 8
        _Stencil ("Stencil ID", Float) = 0
        _StencilOp ("Stencil Operation", Float) = 0
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil Read Mask", Float) = 255

        _ColorMask ("Color Mask", Float) = 15

        [Toggle(UNITY_UI_ALPHACLIP)] _UseUIAlphaClip ("Use Alpha Clip", Float) = 0
    }

    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }

        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }

        Cull Off
        Lighting Off
        ZWrite Off
        ZTest [unity_GUIZTestMode]
        Blend SrcAlpha OneMinusSrcAlpha
        ColorMask [_ColorMask]

        Pass
        {
            Name "Default"
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0

            #include "UnityCG.cginc"
            #include "UnityUI.cginc"

            #pragma multi_compile_local _ UNITY_UI_CLIP_RECT
            #pragma multi_compile_local _ UNITY_UI_ALPHACLIP

            struct appdata_t
            {
                float4 vertex   : POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                fixed4 color    : COLOR;
                float2 texcoord  : TEXCOORD0;
                float2 maskcoord : TEXCOORD1;
                float4 worldPosition : TEXCOORD2;
                UNITY_VERTEX_OUTPUT_STEREO
            };

            sampler2D _MainTex;
            sampler2D _MaskTex;
            fixed4 _Color;
            fixed4 _TextureSampleAdd;
            float4 _ClipRect;
            float4 _MainTex_ST;
            float4 _MaskTex_ST;
            float4 _MaskOffset;
            float4 _MainTexUV;
            float4 _MaskTexUV;

            v2f vert(appdata_t v)
            {
                v2f OUT;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(OUT);
                OUT.worldPosition = v.vertex;
                OUT.vertex = UnityObjectToClipPos(OUT.worldPosition);

                // 使用自定义UV参数来控制主纹理的UV，而不依赖RawImage的uvRect
                // _MainTexUV.xy = offset, _MainTexUV.zw = scale
                OUT.texcoord = v.texcoord * _MainTexUV.zw + _MainTexUV.xy;

                // 计算遮罩纹理坐标，支持图集中的小图
                // _MaskTexUV.xy = offset, _MaskTexUV.zw = scale
                // FairyGUI使用翻转的Y坐标系，需要处理
                float2 maskUV = _MaskTexUV.xy + v.texcoord * _MaskTexUV.zw;
                // 翻转Y坐标以匹配FairyGUI的坐标系
                maskUV.y = 1.0 - maskUV.y;
                OUT.maskcoord = maskUV;

                OUT.color = v.color * _Color;
                return OUT;
            }

            fixed4 frag(v2f IN) : SV_Target
            {
                half4 color = (tex2D(_MainTex, IN.texcoord) + _TextureSampleAdd) * IN.color;

                // 采样遮罩纹理
                half4 mask = tex2D(_MaskTex, IN.maskcoord);

                // 保留空白区域的处理方式
                // 对于拼图块，空白区域也是有意义的，需要保留其形状
                if (mask.a < 0.01)
                {
                    // 在空白区域显示透明色，但不完全丢弃
                    // 这样可以保持拼图块的完整形状，包括空白部分
                    #ifdef UNITY_UI_CLIP_RECT
                    half clipAlpha = UnityGet2DClipping(IN.worldPosition.xy, _ClipRect);
                    return fixed4(0, 0, 0, 0) * clipAlpha;
                    #else
                    return fixed4(0, 0, 0, 0);
                    #endif
                }

                // 使用遮罩的alpha通道来控制最终的透明度
                // 这样只有遮罩形状内的区域才会显示
                color.a *= mask.a;

                #ifdef UNITY_UI_CLIP_RECT
                color.a *= UnityGet2DClipping(IN.worldPosition.xy, _ClipRect);
                #endif

                #ifdef UNITY_UI_ALPHACLIP
                clip (color.a - 0.001);
                #endif

                return color;
            }
            ENDCG
        }
    }
}
