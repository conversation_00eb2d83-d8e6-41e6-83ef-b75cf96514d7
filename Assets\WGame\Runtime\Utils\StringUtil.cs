using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class StringUtil
{
	public static string[] Split(string str, char c)
	{
		if(Empty(str))
			return new string[0];

		return str.Split(new char[]{c});
	}

	public static int[] SplitToInt(string str, char c)
	{
		if(Empty(str))
			return new int[0];

		string[] strArr = Split(str, c);
		int[] intArr = new int[strArr.Length];
		for(int i=0; i<strArr.Length; i++)
		{
			intArr[i] = ToInt(strArr[i]);
		}
		return intArr;
	}

	public static float[] SplitToFloat(string str, char c)
	{
		if(Empty(str))
			return new float[0];

		string[] strArr = Split(str, c);
		float[] floatArr = new float[strArr.Length];
		for(int i=0; i<strArr.Length; i++)
		{
			floatArr[i] = ToFloat(strArr[i]);
		}
		return floatArr;
	}


	public static bool Empty(string str)
	{
		return str == null || str == "";
	}

	public static string FillZero(string str, int count)
	{
		str = "0000000000000000"+str;
		str = str.Substring(str.Length-count, count);
		return str;
	}

	public static string FillZero(int value, int count)
	{
		return FillZero(value.ToString(), count);
	}

	public static int ToInt(string str)
	{
		if(StringUtil.Empty(str))
		{
			return 0;
		}
		return int.Parse(str);
	}

	public static uint ToUint(string str)
	{
		if(StringUtil.Empty(str))
		{
			return 0;
		}
		return uint.Parse(str);
	}

	public static long ToLong(string str)
	{
		long result = 0;
		long.TryParse (str, out result);
		return result;
	}

	public static float ToFloat(string str)
	{
		if(StringUtil.Empty(str))
		{
			return 0;
		}
		return float.Parse(str);
	}

    public static double ToDouble(string str)
    {
        if(string.IsNullOrEmpty(str))
        {
			return 0;
        }
		return double.Parse(str);
    }

	public static bool ToBool(string str)
	{
		str = str.ToLower();
		if(str.Equals("true"))
		{
			return true;
		}
		else if(str.Equals("false"))
		{
			return false;
		}

		int value = ToInt(str);
		if(value > 0)
			return true;
		return false;
	}


	public static string Join(List<string> list, string separator)
	{
		string str = "";
		for (int i = 0; i < list.Count; i++)
		{
			str += list[i];
			if (i < list.Count - 1)
				str += separator;
		}
		return str;
	}

	public static string Join(object[] arr, string separator)
	{
		string str = "";
		for (int i = 0; i < arr.Length; i++)
		{
			str += arr[i];
			if (i < arr.Length - 1)
				str += separator;
		}
		return str;
	}

	public static string Join(string[] arr, string separator)
	{
		string str = "";
		for (int i = 0; i < arr.Length; i++)
		{
			str += arr[i];
			if (i < arr.Length - 1)
				str += separator;
		}
		return str;
	}

	public static string Join(int[] arr, string separator)
	{
		string str = "";
		for (int i = 0; i < arr.Length; i++)
		{
			str += arr[i];
			if (i < arr.Length - 1)
				str += separator;
		}
		return str;
	}

    [System.Obsolete]
    public static string LimitInput(string text, int limit)
	{
		string oldText = text;
		string newText = WWW.EscapeURL (text);
		int delta = (newText.Length - oldText.Length)/8;
		int newLength = oldText.Length + delta;
		if (newLength > limit) {
			int reduce = newLength - limit;
			oldText = oldText.Substring (0, oldText.Length - reduce);
		}
		return oldText;
	}

	public static List<char> InputValidateCharList = new List<char>{'`', '&', '\\'};


	public static char InputValidateHandler(string input, int charIndex, char addedChar)
	{
		//Checks if a dollar sign is entered....
		if (InputValidateCharList.Contains(addedChar))
		{
			// ... if it is change it to an empty character.
			addedChar = '\0';
		}
		return addedChar;
	}


	public static bool Contains(string origin, params string[] keys)
	{
		for (int i = 0; i < keys.Length; i++)
		{
			if (origin.Contains(keys[i]))
				return true;
		}
		return false;
	}
}


