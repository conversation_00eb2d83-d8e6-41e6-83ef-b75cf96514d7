#if DYGAME
using UnityEditor;
using System.IO;
using System.Threading.Tasks;
using TTSDK.Tool.API;
using TTSDK.Tool;
using UnityEngine;

class BuildDygame
{
    [MenuItem("Build/BuildDygame", priority = 1)]
    private static void BuildDygameTool()
    {
        // 执行构建
        BuildGameAsync().ConfigureAwait(false);
    }

    private static async Task BuildGameAsync()
    {
        try
        {
            // 使用 Wasm 框架构建，batchmode 下设置 confirm 为 false
            string outputPath = await BuildManager.Build(Framework.Wasm, false);

            // 输出构建结果
            Debug.Log($"构建完成，输出路径: {outputPath}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"构建失败: {e.Message}");
        }
    }
}
#endif