// Upgrade NOTE: replaced '_Object2World' with 'unity_ObjectToWorld'
// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "FairyGUI/PuzzlePieceMasked"
{
    Properties
    {
        _MainTex ("Base (RGB), Alpha (A)", 2D) = "black" {}
        _MaskTex ("Mask Texture", 2D) = "white" {}
        _ImageSize ("Image Size", Vector) = (1,1,0,0)
        _MaskSize ("Mask Size", Vector) = (1,1,0,0)
        _MaskOriginalSize ("Mask Original Size", Vector) = (1,1,0,0)
        _MaskOffset ("Mask Offset", Vector) = (0,0,0,0)

        // Bevel Properties
        _BevelWidth ("Bevel Width", Range(0, 10)) = 6.0
        _BevelStrength ("Bevel Strength", Range(0, 2)) = 0.5
        _BevelHighlightColor ("Bevel Highlight Color", Color) = (1,1,1,0.6)
        _BevelShadowColor ("Bevel Shadow Color", Color) = (0,0,0,0.5)

        _StencilComp ("Stencil Comparison", Float) = 8
        _Stencil ("Stencil ID", Float) = 0
        _StencilOp ("Stencil Operation", Float) = 0
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil Read Mask", Float) = 255

        _ColorMask ("Color Mask", Float) = 15

        _BlendSrcFactor ("Blend SrcFactor", Float) = 5
        _BlendDstFactor ("Blend DstFactor", Float) = 10
    }
    
    SubShader
    {
        LOD 100

        Tags
        {
            "Queue" = "Transparent"
            "IgnoreProjector" = "True"
            "RenderType" = "Transparent"
        }
        
        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp] 
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }

        Cull Off
        Lighting Off
        ZWrite Off
        Fog { Mode Off }
        Blend [_BlendSrcFactor] [_BlendDstFactor], One One
        ColorMask [_ColorMask]

        Pass
        {
            CGPROGRAM
                #pragma multi_compile NOT_COMBINED COMBINED
                #pragma multi_compile NOT_GRAYED GRAYED COLOR_FILTER
                #pragma multi_compile NOT_CLIPPED CLIPPED SOFT_CLIPPED ALPHA_MASK
                #pragma vertex vert
                #pragma fragment frag
                
                #include "UnityCG.cginc"
    
                struct appdata_t
                {
                    float4 vertex : POSITION;
                    fixed4 color : COLOR;
                    float4 texcoord : TEXCOORD0;
                };
    
                struct v2f
                {
                    float4 vertex : SV_POSITION;
                    fixed4 color : COLOR;
                    float4 texcoord : TEXCOORD0;
                    float2 maskcoord : TEXCOORD1;
                    float2 content_uv : TEXCOORD2; // For boundary check

                    #ifdef CLIPPED
                    float2 clipPos : TEXCOORD3;
                    #endif

                    #ifdef SOFT_CLIPPED
                    float2 clipPos : TEXCOORD3;
                    #endif
                };
    
                sampler2D _MainTex;
                sampler2D _MaskTex;
                float4 _MainTex_TexelSize;
                float4 _MaskTex_TexelSize;
                float4 _MainTexUV;
                float4 _MaskTexUV;
                float4 _MaskNormUV;
                float4 _ImageSize;
                float4 _MaskSize;
                float4 _MaskOriginalSize;
                float4 _MaskOffset;

                // Bevel Properties
                float _BevelWidth;
                float _BevelStrength;
                fixed4 _BevelHighlightColor;
                fixed4 _BevelShadowColor;

                #ifdef COMBINED
                sampler2D _AlphaTex;
                #endif

                CBUFFER_START(UnityPerMaterial)
                #ifdef CLIPPED
                float4 _ClipBox = float4(-2, -2, 0, 0);
                #endif

                #ifdef SOFT_CLIPPED
                float4 _ClipBox = float4(-2, -2, 0, 0);
                float4 _ClipSoftness = float4(0, 0, 0, 0);
                #endif
                CBUFFER_END

                #ifdef COLOR_FILTER
                float4x4 _ColorMatrix;
                float4 _ColorOffset;
                float _ColorOption = 0;
                #endif

                v2f vert (appdata_t v)
                {
                    v2f o;
                    o.vertex = UnityObjectToClipPos(v.vertex);
                    o.texcoord = v.texcoord;

                    // 主纹理UV计算：让主纹理铺满整个UI区域
                    float2 vertex_uv = v.texcoord.xy / v.texcoord.w;
                    o.texcoord.xy = vertex_uv * _MainTexUV.zw + _MainTexUV.xy;
                    o.texcoord.zw = v.texcoord.zw;
                    
                    // 计算归一化的局部UV，用于遮罩
                    float2 local_uv = (vertex_uv - _MaskNormUV.xy) / _MaskNormUV.zw;

                    // FairyGUI的坐标系统分析：
                    // - _MaskOriginalSize: 原始图片尺寸（包含透明区域）190x190
                    // - _MaskSize: 实际内容尺寸（去除透明区域后）
                    // - _MaskOffset: 实际内容在原始图片中的偏移位置（从左上角开始）
                    //
                    // 问题：不同的拼图片段有不同的实际尺寸和偏移，但UI显示尺寸相同
                    // 解决方案：需要将UI坐标正确映射到遮罩纹理的实际内容区域

                    // 尝试Y轴翻转的方法，因为FairyGUI使用左上角为原点，而Unity使用左下角
                    float2 adjusted_offset = _MaskOffset.xy;
                    adjusted_offset.y = _MaskOriginalSize.y - _MaskSize.y - _MaskOffset.y;
                    float2 content_uv = (local_uv * _MaskOriginalSize.xy - adjusted_offset) / _MaskSize.xy;

                    // Pass content_uv for clipping
                    o.content_uv = content_uv;
                    // o.maskcoord = content_uv * _MaskTexUV.zw + _MaskTexUV.xy;
                    o.maskcoord = (local_uv * _MaskOriginalSize.xy - adjusted_offset) / _MaskSize.xy * _MaskTexUV.zw + _MaskTexUV.xy;

                    #if !defined(UNITY_COLORSPACE_GAMMA) && (UNITY_VERSION >= 550)
                    o.color.rgb = GammaToLinearSpace(v.color.rgb);
                    o.color.a = v.color.a;
                    #else
                    o.color = v.color;
                    #endif

                    #ifdef CLIPPED
                    o.clipPos = mul(unity_ObjectToWorld, v.vertex).xy * _ClipBox.zw + _ClipBox.xy;
                    #endif

                    #ifdef SOFT_CLIPPED
                    o.clipPos = mul(unity_ObjectToWorld, v.vertex).xy * _ClipBox.zw + _ClipBox.xy;
                    #endif

                    return o;
                }
                
                fixed4 frag (v2f i) : SV_Target
                {
                    // Clip pixels outside the valid content area
                    clip(i.content_uv.x);
                    clip(1.0 - i.content_uv.x);
                    clip(i.content_uv.y);
                    clip(1.0 - i.content_uv.y);

                    fixed4 col = tex2D(_MainTex, i.texcoord.xy / i.texcoord.w) * i.color;

                    #ifdef COMBINED
                    col.a *= tex2D(_AlphaTex, i.texcoord.xy / i.texcoord.w).g;
                    #endif

                    // Apply mask - 使用遮罩的alpha通道来控制透明度
                    fixed4 maskColor = tex2D(_MaskTex, i.maskcoord);

                    // --- Bevel Calculation ---
                    // Only apply bevel if the mask is somewhat opaque
                    if (maskColor.a > 0.01) // Threshold to avoid processing fully transparent areas
                    {
                        // Calculate normalized pixel offset for bevel sampling
                        // Use _MaskTex_TexelSize for resolution-independent pixel offsets
                        float2 pixelOffset = _MaskTex_TexelSize.xy * _BevelWidth * 0.5; // 0.5 because we sample on both sides

                        // Sample mask alpha at points above and below the current pixel
                        float alpha_current = maskColor.a;
                        float alpha_above = tex2D(_MaskTex, i.maskcoord + float2(0, pixelOffset.y)).a;
                        float alpha_below = tex2D(_MaskTex, i.maskcoord - float2(0, pixelOffset.y)).a;

                        // Calculate the vertical "slope" or difference in alpha.
                        float vertical_diff = (alpha_below - alpha_above);

                        // Modulate by strength
                        float bevel_intensity = vertical_diff * _BevelStrength;

                        // Calculate highlight and shadow factors (ensure they are mutually exclusive)
                        float highlight_factor = saturate(bevel_intensity);  // Only positive diffs contribute
                        float shadow_factor = saturate(-bevel_intensity); // Only negative diffs (inverted) contribute

                        // Apply highlight: Lerp towards bevel highlight color
                        col.rgb = lerp(col.rgb, _BevelHighlightColor.rgb, highlight_factor * _BevelHighlightColor.a);

                        // Apply shadow: Lerp towards bevel shadow color
                        col.rgb = lerp(col.rgb, _BevelShadowColor.rgb, shadow_factor * _BevelShadowColor.a);
                    }

                    col.a *= maskColor.a;

                    #ifdef GRAYED
                    fixed grey = dot(col.rgb, fixed3(0.299, 0.587, 0.114));
                    col.rgb = fixed3(grey, grey, grey);
                    #endif

                    #ifdef SOFT_CLIPPED
                    float2 factor = float2(0,0);
                    if(i.clipPos.x<0)
                        factor.x = (1.0-abs(i.clipPos.x)) * _ClipSoftness.x;
                    else
                        factor.x = (1.0-i.clipPos.x) * _ClipSoftness.z;
                    if(i.clipPos.y<0)
                        factor.y = (1.0-abs(i.clipPos.y)) * _ClipSoftness.w;
                    else
                        factor.y = (1.0-i.clipPos.y) * _ClipSoftness.y;
                    col.a *= clamp(min(factor.x, factor.y), 0.0, 1.0);
                    #endif

                    #ifdef CLIPPED
                    float2 factor = abs(i.clipPos);
                    col.a *= step(max(factor.x, factor.y), 1);
                    #endif

                    #ifdef COLOR_FILTER
                    if (_ColorOption == 0)
                    {
                        fixed4 col2 = col;
                        col2.r = dot(col, _ColorMatrix[0]) + _ColorOffset.x;
                        col2.g = dot(col, _ColorMatrix[1]) + _ColorOffset.y;
                        col2.b = dot(col, _ColorMatrix[2]) + _ColorOffset.z;
                        col2.a = dot(col, _ColorMatrix[3]) + _ColorOffset.w;
                        col = col2;
                    }
                    else //premultiply alpha
                        col.rgb *= col.a;
                    #endif

                    #ifdef ALPHA_MASK
                    clip(col.a - 0.001);
                    #endif

                    return col;
                }
            ENDCG
        }
    }
}
