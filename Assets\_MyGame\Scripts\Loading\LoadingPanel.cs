﻿using FairyGUI;
using UnityEngine;

public class LoadingPanel
{
    private LoadingPanel() { }

    public static float speed = 300;
    private static float currentValue;
    private static float targetValue;
    private static float maxValue;
    private static GLoader logo;
    private static GImage loadingIcon;
    private static float barWidth;
    private static float barX;
    public static void Show()
    {
        _hideWhenFullProgress = false;
        Instance._Show();
        currentValue = 0;
        targetValue = 0;
        maxValue = 100;
        loadingIcon.x = barX;
        SetProgress(1);
        if (progress != null)
        {
            progress.value = 0;
        }
        Timers.inst.AddUpdate(OnUpdate);
    }

    private static void OnUpdate(object param)
    {
        if (progress != null)
        {
            currentValue = Mathf.Min(currentValue + speed * Time.unscaledDeltaTime, targetValue);
            progress.value = currentValue;
            loadingIcon.x = barX + barWidth * currentValue / maxValue;
            if (progress.value >= maxValue && _hideWhenFullProgress)
            {
                Hide();
            }
        }
    }

    public static void Hide()
    {
        Timers.inst.Remove(OnUpdate);
        Instance._Hide();
    }

    private static bool _hideWhenFullProgress;
    public static void HideWhenFullProgress()
    {
        _hideWhenFullProgress = true;
    }

    public static void SetProgress(float value)
    {
        targetValue = value * maxValue;
    }


    private GComponent contentPane;
    private void _Show()
    {
        GRoot.inst.AddChild(contentPane);
    }
    private void _Hide()
    {
        contentPane.RemoveFromParent();
    }

    private static LoadingPanel _instance;
    private static GProgressBar progress;
    private static LoadingPanel Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = new LoadingPanel();
                UIPackage.AddPackage("FGUI/Loading/Loading");
                _instance.contentPane = UIPackage.CreateObject("Loading", "LoadingPanel").asCom;
                _instance.contentPane.sortingOrder = 99999;

                progress = _instance.contentPane.GetChild("progressBar").asProgress;
                var bar = progress.GetChild("bar");
                barX = bar.x;
                barWidth = bar.initWidth;
                loadingIcon = progress.GetChild("icon").asImage;

            }
            _instance.contentPane.MakeFullScreen();
            return _instance;
        }
    }

}
