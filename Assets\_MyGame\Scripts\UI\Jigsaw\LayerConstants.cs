/// <summary>
/// 层级常量定义
/// </summary>
public static class LayerConstants
{
    // 每个层级预留足够的索引空间
    public const int GRID_LAYER = 0;
    public const int THICKNESS_BASE = 1000;           // 1000-1999
    public const int PIECE_BASE = 2000;               // 2000-2999  
    public const int OVERLAY_THICKNESS_BASE = 3000;   // 3000-3999 (支持多层)
    public const int OVERLAY_PIECE_BASE = 4000;       // 4000-4999 (支持多层)
    public const int DRAGGING_THICKNESS_BASE = 5000;  // 5000-5999
    public const int DRAGGING_PIECE_BASE = 6000;      // 6000-6999
    
    public const int LAYER_SPACING = 1000;
    public const int MAX_PIECES_PER_GRID = 100;       // 每个格子最多100个拼块
    public const int MAX_OVERLAY_LAYERS = 10;         // 最大叠加层数
}