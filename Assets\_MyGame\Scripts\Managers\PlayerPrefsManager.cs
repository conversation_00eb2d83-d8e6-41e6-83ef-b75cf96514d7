using UnityEngine;
using System.Collections;



#if UNITY_EDITOR
using CustomPlayerPrefs = UnityEngine.PlayerPrefs;
#else
#if DYGAME
using CustomPlayerPrefs = TTSDK.PlayerPrefs;
#elif WXGAME
using CustomPlayerPrefs = WxPlayerPrefs;
#else
using CustomPlayerPrefs = UnityEngine.PlayerPrefs;
#endif
#endif

#if WXGAME
using WeChatWASM;
class WxPlayerPrefs
{
	public static void SetInt(string key, int value)
	{
		WX.StorageSetIntSync(key, value);
	}

	public static void SetFloat(string key, float value)
	{
		WX.StorageSetFloatSync(key, value);
	}

	public static void SetString(string key, string value)
	{
		WX.StorageSetStringSync(key, value);
	}

	public static int GetInt(string key, int defaultValue = 0)
	{
		return WX.StorageGetIntSync(key, defaultValue);
	}

	public static float GetFloat(string key, float defaultValue = 0)
	{
		return WX.StorageGetFloatSync(key, defaultValue);
	}

	public static string GetString(string key, string defaultValue = "")
	{
		return WX.StorageGetStringSync(key, defaultValue);
	}

	public static bool Has<PERSON>ey(string key)
	{
		return WX.StorageHasKeySync(key);
	}

	public static void DeleteKey(string key)
	{
		WX.StorageDeleteKeySync(key);
	}

	public static void DeleteAll()
	{
		WX.StorageDeleteAllSync();
	}

	public static void Save()
	{
	}
}

#endif
public class PlayerPrefsManager
{
	public static void Set(string title, string label, object value)
	{
		string key = title + "_" + label;
		Set(key, value);
	}

	public static void Set(string title, int id, object value)
	{
		string key = title + "_" + id;
		Set(key, value);
	}

	public static void Set(string key, object value)
	{
		if (value.GetType().Equals(typeof(int)))
		{
			CustomPlayerPrefs.SetInt(key, (int)value);
		}
		else if (value.GetType().Equals(typeof(float)))
		{
			CustomPlayerPrefs.SetFloat(key, (float)value);
		}
		else if (value.GetType().Equals(typeof(string)))
		{
			CustomPlayerPrefs.SetString(key, (string)value);
		}
		Save();
	}

	public static int GetInt(string key, int defaultValue = 0)
	{
		return CustomPlayerPrefs.GetInt(key, defaultValue);
	}

	public static float GetFloat(string key, float defaultValue = 0)
	{
		return CustomPlayerPrefs.GetFloat(key, defaultValue);
	}

	public static string GetString(string key, string defaultValue = "")
	{
		return CustomPlayerPrefs.GetString(key, defaultValue);
	}

	public static bool HasKey(string key)
	{
		return CustomPlayerPrefs.HasKey(key);
	}

	public static void DeleteKey(string key)
	{
		CustomPlayerPrefs.DeleteKey(key);
	}

	public static void DeleteAll()
	{
		CustomPlayerPrefs.DeleteAll();
	}

	public static void Save()
	{
		CustomPlayerPrefs.Save();
	}
}


