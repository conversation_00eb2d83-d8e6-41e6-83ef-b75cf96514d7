using FairyGUI;
using UnityEngine;

public class GameRoot : MonoBehaviour
{
    void Start()
    {
        AssetBundleManager.Initialize("Bundles/");
        UIObjectFactory.SetLoaderExtension(() =>
        {
            return new MyGLoader();
        });
        
        // 创建JagsawPanel并设置网格显示
        Panel.Create<JigsawPanel>((jagsawPanel) => {
            // 默认显示网格
            jagsawPanel.SetGridVisible(true);
        });
    }
}