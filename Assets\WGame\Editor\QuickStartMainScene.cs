﻿
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

public class QuickStartMainScene : MonoBehaviour
{
    [MenuItem("Tools/PlayMainScene", priority = 0)]
    public static void Play()
    {
        if (EditorBuildSettings.scenes.Length == 0)
        {
            Debug.LogError("No scenes in Build Settings");
            return;
        }
        EditorSceneManager.OpenScene(SceneUtility.GetScenePathByBuildIndex(0));
        EditorApplication.ExecuteMenuItem("Edit/Play");
    }
}
#endif