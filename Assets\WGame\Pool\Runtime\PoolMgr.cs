﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;

public class PoolMgr : MonoBehaviour
{
    private static PoolMgr _inst;
    public static PoolMgr Inst
    {
        get
        {
            if (_inst == null)
            {
                var go = new GameObject();
                go.name = "PoolManager";
                _inst = go.AddComponent<PoolMgr>();
                DontDestroyOnLoad(go);
            }
            return _inst;
        }
    }

    [SerializeField] PoolSettingData[] setting;
    [NonSerialized] public bool isReady;
    Dictionary<string, GameObject> prefabDic = new Dictionary<string, GameObject>();
    private Dictionary<string, SimplePool<PoolItem>> poolDic = new Dictionary<string, SimplePool<PoolItem>>();

    public Task PrepareRes(Action<float> OnProgress = null)
    {
        var curCount = 0;
        var totalCount = 0;
        for (int i = 0; i < setting.Length; i++)
        {
            totalCount += setting[i].poolDatas.Length;
        }

        var tcs = new TaskCompletionSource<bool>();
        if (totalCount == 0)
        {
            tcs.SetResult(true);
        }
        for (int i = 0; i < setting.Length; i++)
        {
            var poolDatas = setting[i].poolDatas;
            for (int j = 0; j < poolDatas.Length; j++)
            {
                var data = poolDatas[j];
                data.Load((prefab) =>
                {

                    var poolName = data.GetPoolName();
                    if (!prefabDic.ContainsKey(poolName))
                    {
                        prefabDic.Add(poolName, prefab);
                    }
                    else
                    {
                        Debug.LogWarning("[PoolMgr] duplicate poolName:" + poolName);
                    }

                    if (!string.IsNullOrEmpty(poolName))
                    {
                        CreatePool(prefab, poolName, data.GetInitCount(), data.GetMaxCount());
                    }
                    else
                    {
                        Debug.LogWarning("[PoolMgr] empty poolName:" + data.prefabRef);
                    }
                    OnProgress?.Invoke((float)curCount / totalCount);

                    curCount++;
                    if (curCount >= totalCount)
                    {
                        isReady = true;
                        tcs.SetResult(true);
                    }

                });
            }
        }
        return tcs.Task;
    }

    public void CreatePool(GameObject prefab, string poolName, int initCount, int maxCount, bool preCreateGo = false)
    {
        var customPool = new CustomPool();
        var pool = customPool.Create(prefab, transform, initCount, maxCount);
        //Log.Debug("poolName:" + poolName+ "  prefab:"+ prefab);
        if (preCreateGo)
        {
            for (int i = 0; i < initCount; i++)
            {
                pool.Release(customPool.CreatePoolItem());
            }
        }
        poolDic.Add(poolName, pool);
    }

    public PoolItem Get(string poolName, bool keepTransform = false)
    {
        poolDic.TryGetValue(poolName, out var pool);
#if UNITY_EDITOR
        if (pool == null)
        {
            Debug.LogError("[PoolMgr] Can't find Pool:" + poolName);
            return null;
        }
#endif
        var item = pool.Get();
        if (keepTransform)
        {
            if (prefabDic.TryGetValue(poolName, out GameObject go))
            {
                item.transform.localPosition = go.transform.localPosition;
                item.transform.localScale = go.transform.localScale;
                item.transform.localRotation = go.transform.localRotation;
            }
        }
        return item;
    }

    public T Get<T>(string poolName) where T : Component
    {
        var item = Get(poolName);
        Component result = null;
        if (item != null)
        {
            item.TryGetComponent(typeof(T), out result);
        }
        return (T)result;
    }

    //public void Release(string poolName, PoolItem item)
    //{
    //    poolDic.TryGetValue(poolName, out var pool);
    //    pool?.Release(item);
    //}

    public void Clear()
    {
        foreach (var item in poolDic)
        {
            item.Value.Clear();
        }
        poolDic.Clear();
        prefabDic.Clear();
    }

    private void OnDestroy()
    {
        Clear();
    }
}