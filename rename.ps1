# 按照正确的顺序重命名：x是列，y是行，按行优先
$basePath = "UIProject\Jagsaw\assets\Mask6x8"
$index = 0

# 遍历8行6列（y是行，x是列）
for ($row = 0; $row -lt 8; $row++) {
    for ($col = 0; $col -lt 6; $col++) {
        $oldName = "piece_${col}_${row}.png"
        $newName = "piece_${index}.png"
        $oldPath = Join-Path $basePath $oldName
        
        if (Test-Path $oldPath) {
            Rename-Item $oldPath $newName
            Write-Host "重命名: $oldName -> $newName (第${row}行第${col}列)"
        }
        $index++
    }
}

Write-Host "按照正确顺序重命名完成！"