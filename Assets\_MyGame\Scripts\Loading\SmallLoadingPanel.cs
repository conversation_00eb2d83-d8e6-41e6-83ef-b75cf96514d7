﻿using FairyGUI;
using UnityEngine;

public class SmallLoadingPanel
{
    public static float delayShowTime = 0.1f;
    private static bool isClosed = true;
    private static float _hideWithTimeout;


    private GComponent contentPane;
    private GTextField txtProgress;
    private void DoInitialize()
    {
        // txtProgress = contentPane.GetChild("txtProgress").asTextField;
    }

    public void SetLabel(string label)
    {
        // if (txtProgress != null)
        // {
        //     txtProgress.text = label;
        //     txtProgress.visible = true;
        // }
    }

    private void _Show()
    {
        GRoot.inst.AddChild(contentPane);
    }
    private void _Hide()
    {
        contentPane.RemoveFromParent();
    }


    public static void Show(string label = null, float hideWithTimeout = 5f)
    {
        // if (label != null)
        // {
        //     Instance.SetLabel(label);
        // }
        // else
        // {
        //     Instance.SetLabel(string.Empty);
        // }
        isClosed = false;
        _hideWithTimeout = hideWithTimeout;
        Timers.inst.Add(delayShowTime, 1, DelayShow);
    }
    private static void DelayShow(object obj)
    {
        if (isClosed) return;
        Instance._Show();
        Timers.inst.Add(_hideWithTimeout, 1, TimeOutHide);
    }

    private static void TimeOutHide(object obj)
    {
        Instance._Hide();
    }

    public static void Hide()
    {
        if (isClosed)
            return;
        Timers.inst.Remove(TimeOutHide);
        isClosed = true;
        Instance._Hide();
    }


    private static SmallLoadingPanel _instance;
    private static SmallLoadingPanel Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = new SmallLoadingPanel();
                UIPackage.AddPackage("FGUI/Loading/Loading");
                _instance.contentPane = UIPackage.CreateObject("Loading", "SmallLoading").asCom;
                _instance.contentPane.sortingOrder = 99998;

                _instance.DoInitialize();
            }
            _instance.contentPane.MakeFullScreen();
            return _instance;
        }
    }
}
