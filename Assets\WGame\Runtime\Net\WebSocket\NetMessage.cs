using Google.Protobuf;
using LitJson;

public class NetMessage
{
    public uint cmd;
    public uint msgIdx;
    public byte[] data;
    public JsonData json;

    public uint GetCmd() { return cmd; }

    public T Parse<T>() where T : IMessage, new()
    {
        T msg = new T();
        msg = (T)msg.Descriptor.Parser.ParseFrom(data);
        UnityEngine.Debug.Log($"[Socket] <color=#FFA500>{GetCmd()}</color> {msg}");
        return msg;
    }

    public static NetMessage FromArray(byte[] bytes)
    {
        using var byteArray = new ByteArray(bytes);
        var cmd = byteArray.ReadUInt();
        var msgIdx = byteArray.ReadUInt();

        var dataStart = 8;
        var dataLen = bytes.Length - dataStart;
        var data = new byte[dataLen];
        if (dataLen != 0)
        {
            byteArray.Read(data, 0, dataLen);
        }

        return new NetMessage { cmd = cmd, msgIdx = msgIdx, data = data };
    }
    public byte[] ToArray()
    {
        using var byteArray = new ByteArray();
        byteArray.WriteUInt(cmd);
        byteArray.WriteUInt(msgIdx);

        if (data != null)
        {
            byteArray.Write(data, 0, data.Length);
        }
        return byteArray.ToArray();
    }
    public static NetMessage FromString(string jsonStr)
    {
        JsonData json = JsonMapper.ToObject(jsonStr);
        var msgIdx = (uint)JsonUtil.ToInt(json, "msgId");
        var data = JsonUtil.ToJson(json, "data");
        var cmd = (uint)JsonUtil.ToInt(data, "cmd");
        return new NetMessage { cmd = cmd, msgIdx = msgIdx, json = data };
    }
}
