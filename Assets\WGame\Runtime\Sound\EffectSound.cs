﻿using UnityEngine;

public class EffectSound : MonoBehaviour {

    private float volume = 1;
	void Awake () 
	{
        
    }

    public EffectSoundShoot Play(string path, float volume = 1f, float pitch = 1f, float duration = float.MaxValue)
    {
        return EffectSoundShoot.Create(path, volume * GetVolume(), Vector3.zero, duration, false, pitch: pitch);
    }

    public EffectSoundShoot Play(string path, Vector3 pos, float volume = 1f, float duration = float.MaxValue)
    {
        return EffectSoundShoot.Create(path, volume * GetVolume(), pos, duration);
    }

    public EffectSoundShoot Loop(string path, float volume=1f, float duration=float.MaxValue)
    {
        return EffectSoundShoot.Create(path, volume * GetVolume(), Vector3.zero, duration, true);
    }

    public void Stop(string path)
    {
        EffectSoundShoot.Stop(path);
    }

    public void StopAll()
    {
        EffectSoundShoot.StopAll();
    }

    public bool Mute
    {
        get
        {
            return GetVolume().Equals(0);
        }
        set
        {
            SetVolume(value ? 0 : 1f);
        }
    }

    public void SetVolume(float volume)
    {
        this.volume = volume;
    }

	public float GetVolume()
	{
		return this.volume;
	}
}
