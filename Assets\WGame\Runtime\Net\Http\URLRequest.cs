using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Networking;

public class URLRequest : MonoBehaviour
{
    public static int timeout = 10;
    public enum Method
    {
        GET,
        POST,
        GetTexture,
    }

    public string url;
    public WWWForm form;
    private bool _isLoading;
    public URLRequestResultEvent ResultEvent;
    public object extra;
    private URLRequestQueueData urlQueueData;
    private UnityWebRequest webRequest;

    private Method method;
    private void Init(string url, URLRequestData requestData, Method method)
    {
        if (requestData != null)
        {
            form = requestData.GetDataForm();
        }

        this.url = url;
        this.method = method;
    }


    void OnDestroy()
    {
        requestList.Remove(this);
    }

    public float progress
    {
        get
        {
            if (webRequest != null)
            {
                return webRequest.downloadProgress;
            }
            return 0;
        }
    }

    public float uploadProgress
    {
        get
        {
            if(webRequest != null)
            {
                return webRequest.uploadProgress;
            }
            return 0;
        }
    }

    IEnumerator Load()
    {
        if(method == Method.GET)
        {
            webRequest = UnityWebRequest.Get(url);
        }
        else if(method == Method.POST)
        {
            webRequest = UnityWebRequest.Post(url, form);
        }else if(method == Method.GetTexture)
        {
            webRequest = UnityWebRequestTexture.GetTexture(url);
        }
        webRequest.timeout = timeout;
        yield return webRequest.SendWebRequest();

        URLRequestResult result = new URLRequestResult(this, webRequest);
        if (ResultEvent != null)
        {
            try
            {
                ResultEvent.Invoke(result);
            }
            catch(Exception e)
            {
                Debug.LogException(e);
            }
            ResultEvent.RemoveAllListeners();
        }

        if(urlQueueData != null)
        {
            urlQueueData.EndLoad();
            urlQueueData = null;
        }

        _isLoading = false;
        webRequest = null;

        Dequeue();
    }

    public bool isLoading
    {
        get
        {
            return _isLoading;
        }
    }

    private static List<URLRequest> requestList = new List<URLRequest>();
    private static List<URLRequestQueueData> queueList = new List<URLRequestQueueData>();
    private static int numRequests = 3;

    public static void Get(string url, UnityAction<URLRequestResult> onSuccess = null, UnityAction<string> onError = null, UnityAction onComplete = null)
    {
        Enqueue(url).ResultEvent.AddListener((result) =>
        {
            if (result.Success)
            {
                onSuccess?.Invoke(result);
            }
            else
            {
                onError?.Invoke(result.GetError());
            }
            onComplete?.Invoke();
        });
    }
    public static void Post(string url, URLRequestData data, UnityAction<URLRequestResult> onSuccess = null, UnityAction<string> onError = null, UnityAction onComplete = null)
    {
        Enqueue(url, data, Method.POST).ResultEvent.AddListener((result) =>
        {
            if (result.Success)
            {
                onSuccess?.Invoke(result);
            }
            else
            {
                onError?.Invoke(result.GetError());
            }
            onComplete?.Invoke();
        });
    }


    public static void GetTexture(string url, UnityAction<URLRequestResult> onSuccess, UnityAction<string> onError = null)
    {
        Enqueue(url, null, Method.GetTexture).ResultEvent.AddListener((result) =>
        {
            if (result.Success)
            {
                onSuccess.Invoke(result);
            }
            else
            {
                onError?.Invoke(result.GetError());
            }
        });
    }

    public static URLRequestQueueData Enqueue(string url, URLRequestData requestData = null, Method method = Method.GET, object extra = null)
    {
        URLRequestQueueData queueData = new URLRequestQueueData(url, requestData, method);
        queueData.extra = extra;
        queueList.Add(queueData);
        Dequeue();
        return queueData;
    }

    public static void Dequeue()
    {
        if (queueList.Count == 0)
        {
            return;
        }

        URLRequestQueueData queueData = queueList[0];

        URLRequest request = null;

        if (requestList.Count < numRequests)
        {
            request = CreateBaseURLRequest(queueData.url, queueData.requestData, queueData.method);
            requestList.Add(request);
        }
        else
        {
            for (int i = 0; i < requestList.Count; i++)
            {
                URLRequest req = requestList[i];
                if (!req.isLoading)
                {
                    request = req;
                    request.Init(queueData.url, queueData.requestData, queueData.method);
                    break;
                }
            }
        }

        if (request != null)
        {
            queueData.StartLoad(request);
            request.urlQueueData = queueData;
            request._isLoading = true;
            request.StartCoroutine(request.Load());
            queueList.RemoveAt(0);
        }
    }

    private static URLRequest CreateBaseURLRequest(string url, URLRequestData requestData = null, Method method = Method.GET)
    {
        GameObject gameObj = new GameObject("URLRequest");
        URLRequest urlRequest = gameObj.AddComponent<URLRequest>();

        urlRequest.Init(url, requestData, method);

        return urlRequest;
    }


}

public class URLRequestQueueData
{
    public string url;
    public URLRequestData requestData;
    public URLRequest.Method method;
    public URLRequestResultEvent ResultEvent = new URLRequestResultEvent();
    public object extra;

    private URLRequest request;
    private bool loaded;
    public URLRequestQueueData(string url, URLRequestData requestData, URLRequest.Method method)
    {
        this.url = url;
        this.requestData = requestData;
        this.method = method;
    }

    public void StartLoad(URLRequest request)
    {
        this.request = request;
        if(request != null)
        {
            request.extra = this.extra;
            request.ResultEvent = this.ResultEvent;
        }
    }

    public void EndLoad()
    {
        this.request = null;
        loaded = true;
    }

    public bool isLoading
    {
        get
        {
            return !loaded;
        }
    }

    public float progress
    {
        get
        {
            if (loaded)
                return 1f;
            else if(request != null)
                return request.progress;
            return 0;
        }
    }

    public float uploadProgress
    {
        get
        {
            if (loaded)
                return 1f;
            else if(request != null)
                return request.uploadProgress;
            return 0;
        }
    }
}

