﻿using System.Collections.Generic;
using FairyGUI;
public class MyGLoader : GLoader
{
    private static readonly string URL_PREFIX_BUNDLE = "bundle://";
    private static readonly int URL_PREFIX_BUNDLE_LENGTH = URL_PREFIX_BUNDLE.Length;
    private static readonly string URL_PREFIX_HTTP = "http";
    // private AssetBundleManager.AssetHandle assetHandle;

    private static Dictionary<string, NTexture> cacheTextureDic = new Dictionary<string, NTexture>();
    override protected void LoadExternal()
    {
        if (url.StartsWith(URL_PREFIX_BUNDLE))
        {
            var bundleUrl = url[URL_PREFIX_BUNDLE_LENGTH..];
            AssetBundleManager.LoadTexture(bundleUrl, (texture) =>
            {
                if (isDisposed)
                {
                    FreeExternal(null);
                    return;
                }

                var nTexture = new NTexture(texture);
                onExternalLoadSuccess(nTexture);
            });
        }
        else if (url.StartsWith(URL_PREFIX_HTTP))
        {
            if (cacheTextureDic.TryGetValue(url, out NTexture nTexture))
            {
                onExternalLoadSuccess(nTexture);
                return;
            }

            URLRequest.GetTexture(url,
                onSuccess: (result) =>
                {
                    if (isDisposed)
                    {
                        return;
                    }

                    if (result.GetUrl() != url)
                        return;

                    if (!cacheTextureDic.TryGetValue(url, out NTexture nTexture))
                    {
                        nTexture = new NTexture(result.GetTexture2D());
                        var releaseUrl = url;
                        nTexture.onRelease += (texture) =>
                        {
                            ReleaseTexture(releaseUrl, texture);
                        };
                        cacheTextureDic[url] = nTexture;
                    }
                    onExternalLoadSuccess(nTexture);
                },
                onError: (msg) =>
                {
                    onExternalLoadFailed();
                }
            );
        }
    }

    private void ReleaseTexture(string url, NTexture texture)
    {
        texture.Dispose();
        cacheTextureDic.Remove(url);
        // Log.Info($"===Dispose texture:{url}");
    }

    override protected void FreeExternal(NTexture texture)
    {
        ReleaseHandle();
    }

    public override void Dispose()
    {
        ReleaseHandle();
        base.Dispose();
    }

    private void ReleaseHandle()
    {
        // if (assetHandle != null)
        // {
        //     assetHandle.Release();
        //     assetHandle = null;
        // }
    }
}