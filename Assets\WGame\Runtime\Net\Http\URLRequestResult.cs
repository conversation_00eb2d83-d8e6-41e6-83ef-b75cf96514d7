using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Networking;

public class URLRequestResultEvent : UnityEvent<URLRequestResult> { }
public class URLRequestResult
{

    public URLRequest target;
    private bool _success;
    private UnityWebRequest www;

    public URLRequestResult(URLRequest target, UnityWebRequest www)
    {
        this.target = target;
        this.www = www;
        this._success = (www.error == null);
    }

    public bool Success
    {
        get { return _success; }
    }

    public string GetUrl()
    {
        return www.url;
    }

    public string GetError()
    {
        return www.error;
    }

    public string GetString()
    {
        return www.downloadHandler.text;
    }

    public Texture2D GetTexture2D()
    {
        return DownloadHandlerTexture.GetContent(www);
    }

    public byte[] GetBytes()
    {
        return www.downloadHandler.data;
    }
}
