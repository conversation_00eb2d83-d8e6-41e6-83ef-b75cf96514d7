Shader "UI/PuzzlePieceThickness"
{
    Properties
    {
        _MaskTex ("Mask Texture", 2D) = "white" {}
        _ThicknessOffset ("Thickness Offset", Vector) = (0,-0.05,0,0)
        _ThicknessColor ("Thickness Color", Color) = (1,0,0,1)
    }

    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "RenderType"="Transparent"
        }

        Cull Off
        Lighting Off
        ZWrite Off
        Blend SrcAlpha OneMinusSrcAlpha

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata_t
            {
                float4 vertex   : POSITION;
                float2 texcoord : TEXCOORD0;
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                float2 texcoord : TEXCOORD0;
            };

            sampler2D _MaskTex;
            float4 _ThicknessOffset;
            fixed4 _ThicknessColor;

            v2f vert(appdata_t v)
            {
                v2f OUT;
                OUT.vertex = UnityObjectToClipPos(v.vertex);
                OUT.texcoord = v.texcoord;
                return OUT;
            }

            fixed4 frag(v2f IN) : SV_Target
            {
                // 采样遮罩纹理
                half4 mask = tex2D(_MaskTex, IN.texcoord);

                // 计算向下偏移的遮罩坐标
                float2 offsetCoord = IN.texcoord + _ThicknessOffset.xy;
                half4 offsetMask = tex2D(_MaskTex, offsetCoord);

                // 复制颜色并应用遮罩
                half4 finalColor = _ThicknessColor;
                finalColor.a = max(mask.a, offsetMask.a);

                return finalColor;
            }
            ENDCG
        }
    }
}
