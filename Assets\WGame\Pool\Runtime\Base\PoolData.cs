﻿using System;
using UnityEngine;

[Serializable]
public class PoolData
{
    public GameObject prefabRef;
    public int initCount = 10;
    public int maxCount = 100;

    public int GetInitCount()
    {
        return initCount;
    }

    public int GetMaxCount()
    {
        return maxCount;
    }

    public string GetPoolName()
    {
        if (Asset == null)
        {
            return string.Empty;
        }
        return Asset.name;
    }

    public GameObject GetPrefabRef()
    {
        return prefabRef;
    }

    protected GameObject Asset { get; set; }
    public void Load(Action<GameObject> callback)
    {
        if(Asset != null)
        {
            callback.Invoke(Asset);
        }
        else
        {
            Asset = prefabRef;
            callback.Invoke(Asset);
        }
    }
}