﻿using System;
using System.Collections;
using System.Diagnostics;
using System.Reflection;
using System.Text;

public static class Log
{
	public static void Trace(string msg)
	{
		UnityEngine.Debug.Log(msg);
	}

    [Conditional("UNITY_EDITOR")]
    public static void Debug(string msg)
	{
		UnityEngine.Debug.Log(msg);
	}
    [Conditional("UNITY_EDITOR")]
    public static void Debug(object msg)
    {
        UnityEngine.Debug.Log(msg);
    }
    public static void Info(string msg)
	{
		UnityEngine.Debug.Log(msg);
	}

	public static void Warning(string msg)
	{
		UnityEngine.Debug.LogWarning(msg);
	}

	public static void Error(string msg)
	{
		UnityEngine.Debug.LogError(msg);
	}

	public static void Error(Exception e)
	{
		UnityEngine.Debug.LogException(e);
	}

	public static void Fatal(string msg)
	{
		UnityEngine.Debug.LogAssertion(msg);
	}

	public static void Trace(string message, params object[] args)
	{
		UnityEngine.Debug.LogFormat(message, args);
	}

	public static void Warning(string message, params object[] args)
	{
		UnityEngine.Debug.LogWarningFormat(message, args);
	}

	public static void Info(string message, params object[] args)
	{
		UnityEngine.Debug.LogFormat(message, args);
	}

	public static void Debug(string message, params object[] args)
	{
		UnityEngine.Debug.LogFormat(message, args);
	}

	public static void Error(string message, params object[] args)
	{
		UnityEngine.Debug.LogErrorFormat(message, args);
	}

	public static void Fatal(string message, params object[] args)
	{
		UnityEngine.Debug.LogAssertionFormat(message, args);
	}

	public static void Msg(object msg)
	{
		Debug(Dumper.DumpAsString(msg));
	}


    public static class Dumper
    {
        private static readonly StringBuilder _text = new StringBuilder("", 1024);

        private static void AppendIndent(int num)
        {
            _text.Append(' ', num);
        }

        private static void DoDump(object obj)
        {
            if (obj == null)
            {
                _text.Append("null");
                _text.Append(",");
                return;
            }

            Type t = obj.GetType();

            //repeat field
            if (obj is IList)
            {
                /*
                _text.Append(t.FullName);
                _text.Append(",");
                AppendIndent(1);
                */

                _text.Append("[");
                IList list = obj as IList;
                foreach (object v in list)
                {
                    DoDump(v);
                }

                _text.Append("]");
            }
            else if (t.IsValueType)
            {
                _text.Append(obj);
                _text.Append(",");
                AppendIndent(1);
            }
            else if (obj is string)
            {
                _text.Append("\"");
                _text.Append(obj);
                _text.Append("\"");
                _text.Append(",");
                AppendIndent(1);
            }
            //else if (obj is ByteString)
            //{
            //    _text.Append("\"");
            //    _text.Append(((ByteString)obj).bytes.Utf8ToStr());
            //    _text.Append("\"");
            //    _text.Append(",");
            //    AppendIndent(1);
            //}
            else if (t.IsArray)
            {
                Array a = (Array)obj;
                _text.Append("[");
                for (int i = 0; i < a.Length; i++)
                {
                    _text.Append(i);
                    _text.Append(":");
                    DoDump(a.GetValue(i));
                }

                _text.Append("]");
            }
            else if (t.IsClass)
            {
                _text.Append($"<{t.Name}>");
                _text.Append("{");
                var fields = t.GetProperties(BindingFlags.Public | BindingFlags.Instance);
                if (fields.Length > 0)
                {
                    foreach (PropertyInfo info in fields)
                    {
                        _text.Append(info.Name);
                        _text.Append(":");
                        object value = info.GetGetMethod().Invoke(obj, null);
                        DoDump(value);
                    }
                }

                _text.Append("}");
            }
            else
            {
                UnityEngine.Debug.LogWarning("unsupport type: " + t.FullName);
                _text.Append(obj);
                _text.Append(",");
                AppendIndent(1);
            }
        }

        public static string DumpAsString(object obj, string hint = "")
        {
            _text.Clear();
            _text.Append(hint);
            DoDump(obj);
            return _text.ToString();
        }
    }

}
