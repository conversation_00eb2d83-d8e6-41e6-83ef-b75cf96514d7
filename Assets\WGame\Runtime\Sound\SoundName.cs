﻿using UnityEngine;

public class SoundName : MonoBehaviour {
    public static string CarCome = "CvCarCome";
    public static string CarLeave = "CvCarLeave";
    public static string Cat = "CvMiao";
    public static string Eat = "CvEat";
    public static string Cover = "CvCover";
    public static string Thief = "CvThief";
    public static string BigPig = "CvBigPig";
    public static string CvAirdropBefore = "CvAirdropBefore";
    public static string Airdrop = "CvAirdrop";
    public static string CvAirdropSuccess = "CvAirdropSuccess";
    public static string CvSuccess = "CvSuccess";
    public static string CvObtain = "CvObtain";
    public static string CvAttack = "CvAttack";
    public static string CvPig = "CvPig";
    public static string CvClose = "CvClose";
    public static string CvPvpEnd = "CvPvpEnd";
    public static string CvCome = "CvCome";
    public static string CvTime = "CvTime";
    public static string CvStart = "CvStart";
    public static string CvEvent = "CvEvent";
    public static string CvShopUp = "CvShopUp";
    public static string CvFood = "CvFood";
}
