using System.Collections.Generic;

public class LanguageManager
{
    public static string CurrentLanguage = "zh";
    private static Dictionary<string, Dictionary<string, string>> translations = new Dictionary<string, Dictionary<string, string>>();
    public static void AddTranslation(string language, Dictionary<string, string> translation)
    {
        translations[language] = translation;
    }

    public static void SwitchLanguage(string language)
    {
        CurrentLanguage = language;
    }

    public static string Get(string key)
    {
        if (translations.TryGetValue(CurrentLanguage, out Dictionary<string, string> langDic))
        {
            if (langDic.TryGetValue(key, out string value))
            {
                return value;
            }
        }
        return key;
    }
}