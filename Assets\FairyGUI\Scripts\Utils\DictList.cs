﻿using UnityEngine;
using System.Collections.Generic;

namespace DashGame
{
    public class DictList<T0, T1>
    {
        private Dictionary<T0, T1> dict;
        private List<T1> list;
        private List<T0> keys;

        public DictList()
        {
            dict = new Dictionary<T0, T1>();
            list = new List<T1>();
            keys = new List<T0>();
        }

        public void Add(T0 key, T1 value)
        {
            if(!dict.ContainsKey(key))
            {
                dict.Add(key, value);
                list.Add(value);
                keys.Add(key);
            }
        }

        public void Set(T0 key, T1 value)
        {
            if(dict.ContainsKey(key))
            {
                dict[key] = value;
                int index = keys.IndexOf(key);
                list[index] = value;
            }
            else
            {
                Add(key, value);
            }
        }

        public void Remove(T0 key)
        {
            T1 value = default(T1);
            if(dict.TryGetValue(key, out value))
            {
                dict.Remove(key);
                list.Remove(value);
                keys.Remove(key);
            }
        }

        public void RemoveAt(int index)
        {
            T0 key = keys[index];
            Remove(key);
        }

        public List<T0> GetKeys()
        {
            return keys;
        }

        public T0 GetKeyByIndex(int index)
        {
            return keys[index];
        }

        public bool ContainsKey(T0 key)
        {
            return dict.ContainsKey(key);
        }

        public T1 GetValueByKey(T0 key)
        {
            T1 value = default(T1);
            dict.TryGetValue(key, out value);
            return value;
        }

        public T1 GetValueByIndex(int index)
        {
            return list[index];
        }

        public void MergeTo(List<T1> list)
        {
            list.AddRange(this.list);
        }

        public List<T1> GetList()
        {
            List<T1> list = new List<T1>();
            MergeTo(list);
            return list;
        }

        public int Count
        {
            get
            {
                return list.Count;
            }
        }

        public void Clear()
        {
            dict.Clear();
            list.Clear();
            keys.Clear();
        }
    }
}

