﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using FairyGUI;

public class MediatorBase
{
    protected GObject gObj;
    //public MediatorBase()
    //{
    //    this.InitView();
    //}
    public MediatorBase(GObject gObj)
    {
        this.gObj = gObj;
        gObj.onClick.Add(OnContentClick);
    }

    private void OnContentClick()
    {
        GObject target = GRoot.inst.touchTarget;
        if (target == null)
            return;
        this.OnMouseClick(target.name);
    }

    //处理界面点击事件
    protected virtual void OnMouseClick(string targetName)
    {

    }

    public virtual void Show()
    {
        if (gObj != null)
        {
            gObj.visible = true;
        }
    }
    public virtual void Hide()
    {
        if(gObj != null)
        {
            gObj.visible = false;
        }
    }

    public virtual void Remove()
    {
        if(gObj != null)
        {
            gObj.onClick.Remove(this.OnContentClick);
        }
        GTween.Kill(this);
    }

    protected void TimerLoop(float interval, int repeat, TimerCallback callBack)
    {
        Timers.inst.Add(interval, repeat, callBack);
    }
    protected void TimerLoop(float interval, int repeat, TimerCallback callBack, object param)
    {
        Timers.inst.Add(interval, repeat, callBack, param);
    }
    protected void TimerRemove(TimerCallback callBack)
    {
        Timers.inst.Remove(callBack);
    }

}
