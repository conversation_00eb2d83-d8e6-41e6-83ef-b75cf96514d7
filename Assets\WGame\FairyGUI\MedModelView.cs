﻿using FairyGUI;
using UnityEngine;

public class MedModelView : MediatorBase
{
    /// <summary>
    /// 自动旋转
    /// </summary>
    public Vector3 autoEulerAngles = new Vector3(0,0.5f,0);
    /// <summary>
    /// 初始旋转
    /// </summary>
    public Vector3 modEulerAngle = new Vector3();
    public float positionz = 100;

    /// <summary>
    /// 坐标空间，主要影响旋转效果
    /// </summary>
    public Space spaceType = Space.World;
    public Vector3 modScale = new Vector3(1,1,1);

    public GGraph holder;
    public GameObject modObj;
    private float offsetX = 0f;
    private float offsetY = 0f;
    private float scaleLength = 1f;

    public MedModelView(GGraph pContainer) : base(pContainer)
    {
        holder = pContainer;
    }

    public void SetGameObject(GameObject prefab, float uiScale = 1f, float offsetX = 0f, float offsetY = 0f, bool cloneMaterial = true)
    {
        this.scaleLength = uiScale;
        this.offsetX = offsetX;
        this.offsetY = offsetY;
        var localScale = prefab.transform.localScale;
        modScale.Set(localScale.x, localScale.y, localScale.z);
        SetNativeObject(prefab, cloneMaterial);
    }

    public void CacheRenderers()
    {
        if (holder.displayObject is GoWrapper)
        {
            (holder.displayObject as GoWrapper).CacheRenderers();
        }
    }

    private void UnCastShadows(GameObject go)
    {
        if (go == null) return;
        Renderer[] renderers = go.GetComponentsInChildren<Renderer>();
        if (renderers != null)
        {
            for (int i = 0; i < renderers.Length; i++)
            {
                renderers[i].shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
            }
        }
    }

    private void SetNativeObject(GameObject mod, bool cloneMaterial = true)
    {
        if (holder == null)
            return;

        if (modObj != null)
        {
            GameObject.Destroy(modObj);
        }
        modObj = mod;

        UnCastShadows(modObj);
        modObj.transform.localScale = modScale * this.scaleLength;
        modObj.transform.position = new Vector3(offsetX, offsetY, positionz);
        // modObj.transform.localEulerAngles = modEulerAngle;
        if (!(holder.displayObject is GoWrapper))
        {
            GoWrapper wrapper = new GoWrapper();
            wrapper.SetWrapTarget(modObj, cloneMaterial);
            holder.SetNativeObject(wrapper);
        }
        else
        {
            (holder.displayObject as GoWrapper).wrapTarget = modObj;
        }
    }

    public override void Hide()
    {
        base.Hide();

        holder = null;
        if (modObj != null)
        {
            GameObject.Destroy(modObj);
            modObj = null;
        }
    }

    public override void Remove()
    {
        Hide();
        base.Remove();
    }
}

