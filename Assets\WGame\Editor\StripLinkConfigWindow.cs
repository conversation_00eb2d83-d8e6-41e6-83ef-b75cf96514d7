using System;
using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;

public class StripLinkConfigWindow : EditorWindow
{
    public const string LinkFile = "Assets/link.xml";
    public const string STRIP_GENERATE_TAG = "<!--GENERATE_TAG-->";
    private const string MatchPattern = "<assembly[\\s]+fullname[\\s]*=[\\s]*\"([^\"]+)\"";
    [MenuItem("Tools/Strip Config Window", false, 1)]
    public static void ShowStripConfigEditor()
    {
        EditorWindow.GetWindow<StripLinkConfigWindow>("Strip LinkConfig Editor").Show();
    }

    private class ItemData
    {
        public bool isOn;
        public string dllName;
        public ItemData(bool isOn, string dllName)
        {
            this.isOn = isOn;
            this.dllName = dllName;
        }
    }
    private Vector2 scrollPosition;
    private string[] selectedDllList;
    private List<ItemData> dataList;
    private GUIStyle normalStyle;
    private GUIStyle selectedStyle;
    private void OnEnable()
    {
        normalStyle = new GUIStyle();
        normalStyle.normal.textColor = Color.white;

        selectedStyle = new GUIStyle();
        selectedStyle.normal.textColor = Color.green;
        dataList = new List<ItemData>();
        RefreshListData();
    }
    private void OnGUI()
    {
        EditorGUILayout.BeginVertical();
        if (dataList.Count <= 0)
        {
            EditorGUILayout.HelpBox("δ�ҵ�����,����Build��Ŀ�����ɳ���.", MessageType.Warning);
        }
        else
        {
            EditorGUILayout.HelpBox("��ѡ��Ҫ���ӵ�Link.xml�ĳ���,Ȼ����������Ч.", MessageType.Info);
        }
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, false, true);
        for (int i = 0; i < dataList.Count; i++)
        {
            EditorGUILayout.BeginHorizontal();
            var item = dataList[i];
            item.isOn = EditorGUILayout.ToggleLeft(item.dllName, item.isOn, item.isOn ? selectedStyle : normalStyle);
            EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.EndScrollView();
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Select All", GUILayout.Width(100)))
        {
            SelectAll(true);
        }

        if (GUILayout.Button("Cancel All", GUILayout.Width(100)))
        {
            SelectAll(false);
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Reload", GUILayout.Width(120)))
        {
            RefreshListData();
        }

        if (GUILayout.Button("Save", GUILayout.Width(120)))
        {
            if (Save2LinkFile(GetCurrentSelectedList()))
            {
                EditorUtility.DisplayDialog("Strip LinkConfig Editor", "Update link.xml success!", "OK");
            }
            AssetDatabase.Refresh();
        }
        EditorGUILayout.EndHorizontal();
        EditorGUILayout.EndVertical();
    }
    private void SelectAll(bool isOn)
    {
        foreach (var item in dataList)
        {
            item.isOn = isOn;
        }
    }
    private string[] GetCurrentSelectedList()
    {
        List<string> result = new List<string>();
        foreach (var item in dataList)
        {
            if (item.isOn)
            {
                result.Add(item.dllName);
            }
        }
        return result.ToArray();
    }
    private void RefreshListData()
    {
        dataList.Clear();
        selectedDllList = GetSelectedAssemblyDlls();
        foreach (var item in GetProjectAssemblyDlls())
        {
            dataList.Add(new ItemData(IsInSelectedList(item), item));
        }
    }
    private bool IsInSelectedList(string dllName)
    {
        return ArrayUtility.Contains(selectedDllList, dllName);
    }

    /// <summary>
    /// ��ȡ��Ŀȫ��dll
    /// </summary>
    /// <returns></returns>
    public static string[] GetProjectAssemblyDlls()
    {
        List<string> dlls = new List<string>();
        var dllDir = GetStripAssembliesDir2021(EditorUserBuildSettings.activeBuildTarget);
        if (!Directory.Exists(dllDir))
        {
            return dlls.ToArray();
        }
        var files = Directory.GetFiles(dllDir, "*.dll", SearchOption.AllDirectories);
        foreach (var file in files)
        {
            var fileInfo = new FileInfo(file);
            var fileName = fileInfo.Name.Substring(0, fileInfo.Name.Length - fileInfo.Extension.Length);
            if (!dlls.Contains(fileName)) dlls.Add(fileName);
        }
        return dlls.ToArray();
    }

#if UNITY_2021_1_OR_NEWER
    public static string GetStripAssembliesDir2021(BuildTarget target)
    {
        string projectDir = Directory.GetParent(Application.dataPath).ToString();
        //return $"{projectDir}/Library/ScriptAssemblies";

#if UNITY_STANDALONE_WIN
        return $"{projectDir}/Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped";
#elif UNITY_ANDROID
            return $"{projectDir}/Library/Bee/artifacts/Android/ManagedStripped";
#elif UNITY_IOS
            return $"{projectDir}/Temp/StagingArea/Data/Managed/tempStrip";
#elif UNITY_WEBGL
            return $"{projectDir}/Library/Bee/artifacts/WebGL/ManagedStripped";
#elif UNITY_EDITOR_OSX
            return $"{projectDir}/Library/Bee/artifacts/MacStandalonePlayerBuildProgram/ManagedStripped";
#else
            throw new NotSupportedException("GetOriginBuildStripAssembliesDir");
#endif
    }
#else
        private string GetStripAssembliesDir2020(BuildTarget target)
        {
            string subPath = target == BuildTarget.Android ?
                "assets/bin/Data/Managed" :
                "Data/Managed/";
            return $"{SettingsUtil.ProjectDir}/Temp/StagingArea/{subPath}";
        }

        public void OnBeforeConvertRun(BuildReport report, Il2CppBuildPipelineData data)
        {            
            // �˻ص�ֻ�� 2020�е���
            CopyStripDlls(GetStripAssembliesDir2020(data.target), data.target);
        }
#endif
    
    /// <summary>
    /// ��ȡ�Ѿ����õ�link.xml���dll
    /// </summary>
    /// <returns></returns>
    public static string[] GetSelectedAssemblyDlls()
    {
        List<string> dlls = new List<string>();
        if (!File.Exists(LinkFile))
        {
            return dlls.ToArray();
        }
        var lines = File.ReadAllLines(LinkFile);
        int generateBeginLine = lines.Length, generateEndLine = lines.Length;
        for (int i = 0; i < lines.Length; i++)
        {
            string line = lines[i];
            if (generateBeginLine >= lines.Length && line.Trim().CompareTo(STRIP_GENERATE_TAG) == 0)
            {
                generateBeginLine = i;
            }
            else if (generateEndLine >= lines.Length && line.Trim().CompareTo(STRIP_GENERATE_TAG) == 0)
            {
                generateEndLine = i;
            }
            if (((i > generateBeginLine && generateEndLine >= lines.Length) || (i > generateBeginLine && i < generateEndLine)) && !string.IsNullOrWhiteSpace(line))
            {
                var match = Regex.Match(line, MatchPattern);
                if (match.Success)
                {
                    var assemblyName = match.Result("$1");
                    if (!dlls.Contains(assemblyName)) dlls.Add(assemblyName);
                }
            }

        }
        return dlls.ToArray();
    }
    public static bool Save2LinkFile(string[] stripList)
    {
        if (!File.Exists(LinkFile))
        {
            File.WriteAllText(LinkFile, $"<linker>{Environment.NewLine}{STRIP_GENERATE_TAG}{Environment.NewLine}{STRIP_GENERATE_TAG}</linker>");
        }
        var lines = File.ReadAllLines(LinkFile);
        FindGenerateLine(lines, out int beginLineIdx, out int endLineIdx);
        int headIdx = ArrayUtility.FindIndex(lines, line => line.Trim().CompareTo("<linker>") == 0);
        if (beginLineIdx >= lines.Length)
        {
            ArrayUtility.Insert(ref lines, headIdx + 1, STRIP_GENERATE_TAG);
        }
        if (endLineIdx >= lines.Length)
        {
            ArrayUtility.Insert(ref lines, headIdx + 1, STRIP_GENERATE_TAG);
        }
        FindGenerateLine(lines, out beginLineIdx, out endLineIdx);
        int insertIdx = beginLineIdx;
        for (int i = 0; i < stripList.Length; i++)
        {
            insertIdx = beginLineIdx + i + 1;
            if (insertIdx >= endLineIdx)
            {
                ArrayUtility.Insert(ref lines, endLineIdx, FormatStripLine(stripList[i]));
            }
            else
            {
                lines[insertIdx] = FormatStripLine(stripList[i]);
            }
        }
        while ((insertIdx + 1) < lines.Length && lines[insertIdx + 1].Trim().CompareTo(STRIP_GENERATE_TAG) != 0)
        {
            ArrayUtility.RemoveAt(ref lines, insertIdx + 1);
        }
        try
        {
            File.WriteAllLines(LinkFile, lines, System.Text.Encoding.UTF8);
            return true;
        }
        catch (Exception e)
        {
            Debug.LogErrorFormat("Save2LinkFile Failed:{0}", e.Message);
            return false;
        }
    }
    private static string FormatStripLine(string assemblyName)
    {
        return $"\t<assembly fullname=\"{assemblyName}\" preserve=\"all\" />";
    }
    private static void FindGenerateLine(string[] lines, out int beginLineIdx, out int endLineIdx)
    {
        beginLineIdx = endLineIdx = lines.Length;
        for (int i = 0; i < lines.Length; i++)
        {
            var line = lines[i];
            if (beginLineIdx >= lines.Length && line.Trim().CompareTo(STRIP_GENERATE_TAG) == 0)
            {
                beginLineIdx = i;
            }
            else if (endLineIdx >= lines.Length && line.Trim().CompareTo(STRIP_GENERATE_TAG) == 0)
            {
                endLineIdx = i;
            }
        }
    }
}