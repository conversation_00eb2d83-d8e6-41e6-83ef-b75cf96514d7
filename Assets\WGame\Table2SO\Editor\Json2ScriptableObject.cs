using System;
using System.IO;
using Unity.VisualScripting;
using UnityEditor;
using UnityEngine;

public class Json2ScriptableObject
{
    private static string OUTPUT_DIR1 = "Assets/_MyGame/Resources/Bundles/DataSO";
    private static string OUTPUT_DIR2 = "Assets/_MyGame/Bundles/DataSO";
    [MenuItem("Tools/Table2SO", priority = 0)]
    public static void Execute()
    {
        Table2Json();
    }

    private static void Table2Json()
    {
        var excelDir = Path.Join(Application.dataPath, "../Excels");
        string batFilePath = Path.Combine(excelDir, "export.bat");
        if (File.Exists(batFilePath))
        {
            var startInfo = new System.Diagnostics.ProcessStartInfo(batFilePath, GameConfig.GetVer());
            startInfo.WorkingDirectory = excelDir;
            var process = new System.Diagnostics.Process();

            // try
            // {
            process.StartInfo = startInfo;
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.CreateNoWindow = true;
                process.OutputDataReceived += (sender, data) => Debug.Log(data.Data);
                process.Start();
                process.BeginOutputReadLine();
                process.WaitForExit();
                Json2SO();
            // }
            // catch (Exception ex)
            // {
            //     Debug.LogError("Error occurred during process execution: " + ex.Message);
            // }
        }
        else
        {
            Debug.LogError("export.bat file not found in " + excelDir);
        }
    }

    private static void Json2SO()
    {
        var tables = TableSO.tableNames;
        if (!Directory.Exists(OUTPUT_DIR1))
        {
            OUTPUT_DIR1 = OUTPUT_DIR2;
        }
        string assetPath = Path.Join(OUTPUT_DIR1, "TableSO.asset");
        if (File.Exists(assetPath))
        {
            File.Delete(assetPath);
        }
        var dir = Path.GetDirectoryName(assetPath);
        if (!Directory.Exists(dir))
        {
            Debug.LogError($"目录不存在:{dir}");
            return;
        }

        var table = ScriptableObject.CreateInstance<TableSO>();
        for (int i = 0; i < tables.Length; i++)
        {
            var tableName = tables[i];
            string path = Path.Join(Application.dataPath, "../Excels/json", $"{tableName}.json");
            if (!File.Exists(path))
            {
                UnityEngine.Debug.LogError($"表格文件不存在：{path}");
                continue;
            }
            string text = File.ReadAllText(path);
            if (string.IsNullOrEmpty(text))
            {
                UnityEngine.Debug.LogError($"表格数据为空：{path}");
            }

            var json = LitJson.JsonMapper.ToObject(text);
            table.GenerateSO(tableName, json);
        }

        AssetDatabase.CreateAsset(table, assetPath);
        EditorUtility.SetDirty(table);
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        Debug.Log("======表格导出完成======");
        Debug.Log(assetPath);
    }
}